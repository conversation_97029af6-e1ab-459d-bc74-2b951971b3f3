#include "AD9833.h"		

//时钟速率为25 MHz时， 可以实现0.1 Hz的分辨率；
//而时钟速率为1 MHz时，则可以实现0.004 Hz的分辨率。
//调整参考时钟修改此处即可
#define FCLK          4096       	           //设置参考时钟 5MHz，选择外部晶振   1M的话100k的波就寄了   4.096M
#define RealFreDat    268435456.0/FCLK     //总的公式为 Fout=（Fclk/2的28次方）*28位寄存器的值

static void AD9833_Delay(void)
{
	uint16_t i;
	for (i = 0; i < 1; i++);
}

/**************************************
*   函 数 名: ad9833_write_data
*   功能说明: ad9833写入16位数据
*   形    参: txdata：待写入的16位数据
*   返 回 值: 无
*************************************/
void AD9833_WriteData_No1(u16 txdata)
{
    int i;
    SCLK_1_No1;
    FSYNC_1_No1;
    FSYNC_0_No1;
    //写16位数据
    for(i=0;i<16;i++)
    {
        if (txdata & 0x8000)
            SDATA_1_No1;
        else
            SDATA_0_No1;
        AD9833_Delay();
        SCLK_0_No1;
        AD9833_Delay();
        SCLK_1_No1;
        txdata<<=1;
    }
    FSYNC_1_No1;
}
void AD9833_WriteData_No2(u16 txdata)
{
    int i;
    SCLK_1_No2;
    FSYNC_1_No2;
    FSYNC_0_No2;
    //写16位数据
    for(i=0;i<16;i++)
    {
        if (txdata & 0x8000)
            SDATA_1_No2;
        else
            SDATA_0_No2;
        AD9833_Delay();
        SCLK_0_No2;
        AD9833_Delay();
        SCLK_1_No2;
        txdata<<=1;
    }
    FSYNC_1_No2;
}


/**************************************
*   函 数 名: AD9833_SetFrequency
*   功能说明: ad9833设置频率寄存器
*   形    参: reg：待写入的频率寄存器   
              fout：频率值
*   返 回 值: 无
*************************************/
void AD9833_SetFrequency_No1(unsigned short reg, double fout)
{
    int frequence_LSB,frequence_MSB;
    double   frequence_mid,frequence_DATA;
    long int frequence_hex;
	
    /*********************************计算频率的16进制值***********************************/
    frequence_mid=RealFreDat;
    //如果时钟频率不为25MHZ，修改该处的频率值，单位MHz ，AD9833最大支持25MHz
    frequence_DATA=fout;
    frequence_DATA=frequence_DATA/1000;  //多少k
    frequence_DATA=frequence_DATA*frequence_mid;
    frequence_hex=frequence_DATA;  //这个frequence_hex的值是32位的一个很大的数字，需要拆分成两个14位进行处理；
    frequence_LSB=frequence_hex; //frequence_hex低16位送给frequence_LSB
    frequence_LSB=frequence_LSB&0x3fff;//去除最高两位，16位数换去掉高位后变成了14位
    frequence_MSB=frequence_hex>>14; //frequence_hex高16位送给frequence_HSB
    frequence_MSB=frequence_MSB&0x3fff;//去除最高两位，16位数换去掉高位后变成了14位
    frequence_LSB=frequence_LSB|reg;
    frequence_MSB=frequence_MSB|reg;
    AD9833_WriteData_No1(0x2100); //选择数据一次写入，B28位和RESET位为1
    AD9833_WriteData_No1(frequence_LSB);
    AD9833_WriteData_No1(frequence_MSB);
}

void AD9833_SetFrequency_No2(unsigned short reg, double fout)
{
    int frequence_LSB,frequence_MSB;
    double   frequence_mid,frequence_DATA;
    long int frequence_hex;
	
    /*********************************计算频率的16进制值***********************************/
    frequence_mid=RealFreDat;//适合25M晶振
    //如果时钟频率不为25MHZ，修改该处的频率值，单位MHz ，AD9833最大支持25MHz
    frequence_DATA=fout;
    frequence_DATA=frequence_DATA/1000;
    frequence_DATA=frequence_DATA*frequence_mid;
    frequence_hex=frequence_DATA;  //这个frequence_hex的值是32位的一个很大的数字，需要拆分成两个14位进行处理；
    frequence_LSB=frequence_hex; //frequence_hex低16位送给frequence_LSB
    frequence_LSB=frequence_LSB&0x3fff;//去除最高两位，16位数换去掉高位后变成了14位
    frequence_MSB=frequence_hex>>14; //frequence_hex高16位送给frequence_HSB
    frequence_MSB=frequence_MSB&0x3fff;//去除最高两位，16位数换去掉高位后变成了14位
    frequence_LSB=frequence_LSB|reg;
    frequence_MSB=frequence_MSB|reg;
    AD9833_WriteData_No2(0x2100); //选择数据一次写入，B28位和RESET位为1
    AD9833_WriteData_No2(frequence_LSB);
    AD9833_WriteData_No2(frequence_MSB);
}

/**************************************
*   函 数 名: ad9833_write_data
*   功能说明: ad9833设置相位寄存器
*   形    参: reg：待写入的相位寄存器   
              fout：相位值
*   返 回 值: 无
*************************************/
void AD9833_SetPhase_No1(unsigned short reg, unsigned short val)
{
    unsigned short phase = reg;
    phase |= val;
    AD9833_WriteData_No1(phase);
}

void AD9833_SetPhase_No2(unsigned short reg, unsigned short val)
{
    unsigned short phase = reg;
    phase |= val;
    AD9833_WriteData_No2(phase);
}
/**************************************
*   函 数 名: AD9833_SetWave
*   功能说明: ad9833设置波形
*   形    参: WaveMode：输出波形类型 
              Freq_SFR：输出的频率寄存器类型
              Phase_SFR：输出的相位寄存器类型
*   返 回 值: 无
*************************************/
void AD9833_SetWave_No1(unsigned int WaveMode,unsigned int Freq_SFR,unsigned int Phase_SFR)
{
    unsigned int val = 0;
    val = (val | WaveMode | Freq_SFR | Phase_SFR);
    AD9833_WriteData_No1(val);
}

void AD9833_SetWave_No2(unsigned int WaveMode,unsigned int Freq_SFR,unsigned int Phase_SFR)
{
    unsigned int val = 0;
    val = (val | WaveMode | Freq_SFR | Phase_SFR);
    AD9833_WriteData_No2(val);
}
/**************************************
*   函 数 名: AD9833_Setup
*   功能说明: ad9833设置输出
*   形    参: Freq_SFR：频率寄存器类型
              Freq    ：频率值
              Phase_SFR：相位寄存器类型
              Phase：相位值
              WaveMode：波形类型
*   返 回 值: 无
*************************************/
void AD9833_Setup_No1(unsigned int Freq_SFR,double Freq,unsigned int Phase_SFR,unsigned int Phase,unsigned int WaveMode)
{
    unsigned int Fsel,Psel;
    AD9833_WriteData_No1(0x0100); //复位AD9833,即RESET位为1
    AD9833_WriteData_No1(0x2100); //选择数据一次写入，B28位和RESET位为1
    AD9833_SetFrequency_No1(Freq_SFR,Freq);
    AD9833_SetPhase_No1(Phase_SFR,Phase);
    if(Freq_SFR == AD9833_REG_FREQ0)
    {
        Fsel = AD9833_FSEL0;
    }
    else 
    {
        Fsel = AD9833_FSEL1;
    }
    if(Phase_SFR == AD9833_REG_PHASE0)
    {
        Psel = AD9833_PSEL0;
    }
    else 
    {
        Psel = AD9833_PSEL1;
    }
    AD9833_SetWave_No1(WaveMode,Fsel,Psel);
}

void AD9833_Setup_No2(unsigned int Freq_SFR,double Freq,unsigned int Phase_SFR,unsigned int Phase,unsigned int WaveMode)
{
    unsigned int Fsel,Psel;
    AD9833_WriteData_No2(0x0100); //复位AD9833,即RESET位为1
    AD9833_WriteData_No2(0x2100); //选择数据一次写入，B28位和RESET位为1
    AD9833_SetFrequency_No2(Freq_SFR,Freq);
    AD9833_SetPhase_No2(Phase_SFR,Phase);
    if(Freq_SFR == AD9833_REG_FREQ0)
    {
        Fsel = AD9833_FSEL0;
    }
    else 
    {
        Fsel = AD9833_FSEL1;
    }
    if(Phase_SFR == AD9833_REG_PHASE0)
    {
        Psel = AD9833_PSEL0;
    }
    else 
    {
        Psel = AD9833_PSEL1;
    }
    AD9833_SetWave_No2(WaveMode,Fsel,Psel);
}
