--cpu=Cortex-M4.fp.sp
"cubemxproject_2023h\startup_stm32f407xx.o"
"cubemxproject_2023h\lcd.o"
"cubemxproject_2023h\calculate.o"
"cubemxproject_2023h\ad9833.o"
"cubemxproject_2023h\si5351a.o"
"cubemxproject_2023h\relays.o"
"cubemxproject_2023h\key.o"
"cubemxproject_2023h\main.o"
"cubemxproject_2023h\gpio.o"
"cubemxproject_2023h\adc.o"
"cubemxproject_2023h\dma.o"
"cubemxproject_2023h\fsmc.o"
"cubemxproject_2023h\tim.o"
"cubemxproject_2023h\usart.o"
"cubemxproject_2023h\stm32f4xx_it.o"
"cubemxproject_2023h\stm32f4xx_hal_msp.o"
"cubemxproject_2023h\stm32f4xx_hal_adc.o"
"cubemxproject_2023h\stm32f4xx_hal_adc_ex.o"
"cubemxproject_2023h\stm32f4xx_ll_adc.o"
"cubemxproject_2023h\stm32f4xx_hal_rcc.o"
"cubemxproject_2023h\stm32f4xx_hal_rcc_ex.o"
"cubemxproject_2023h\stm32f4xx_hal_flash.o"
"cubemxproject_2023h\stm32f4xx_hal_flash_ex.o"
"cubemxproject_2023h\stm32f4xx_hal_flash_ramfunc.o"
"cubemxproject_2023h\stm32f4xx_hal_gpio.o"
"cubemxproject_2023h\stm32f4xx_hal_dma_ex.o"
"cubemxproject_2023h\stm32f4xx_hal_dma.o"
"cubemxproject_2023h\stm32f4xx_hal_pwr.o"
"cubemxproject_2023h\stm32f4xx_hal_pwr_ex.o"
"cubemxproject_2023h\stm32f4xx_hal_cortex.o"
"cubemxproject_2023h\stm32f4xx_hal.o"
"cubemxproject_2023h\stm32f4xx_hal_exti.o"
"cubemxproject_2023h\stm32f4xx_ll_fsmc.o"
"cubemxproject_2023h\stm32f4xx_hal_nor.o"
"cubemxproject_2023h\stm32f4xx_hal_sram.o"
"cubemxproject_2023h\stm32f4xx_hal_nand.o"
"cubemxproject_2023h\stm32f4xx_hal_pccard.o"
"cubemxproject_2023h\stm32f4xx_hal_tim.o"
"cubemxproject_2023h\stm32f4xx_hal_tim_ex.o"
"cubemxproject_2023h\stm32f4xx_hal_uart.o"
"..\Drivers\CMSIS\arm_cortexM4lf_math.lib"
"cubemxproject_2023h\system_stm32f4xx.o"
--library_type=microlib --strict --scatter "CubeMXProject_2023H\CubeMXProject_2023H.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "CubeMXProject_2023H.map" -o CubeMXProject_2023H\CubeMXProject_2023H.axf