/**
 * @file dpll_simple.c
 * @brief 简化的数字锁相环实现
 * <AUTHOR> Assistant
 * @date 2025
 */

#include "dpll_simple.h"
#include "AD9833.h"
#include "main.h"
#include "arm_math.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 简化的锁相环结构
typedef struct {
    float target_freq;      // 目标频率
    float current_phase;    // 当前相位
    float phase_error;      // 相位误差
    bool is_locked;        // 锁定状态
    uint32_t lock_counter; // 锁定计数器
} SimpleDPLL_t;

static SimpleDPLL_t dpll_ch1, dpll_ch2;
static bool dpll_simple_enabled = false;

/**
 * @brief 简化锁相环初始化
 */
void DPLL_Simple_Init(float freq1, float freq2) {
    dpll_ch1.target_freq = freq1;
    dpll_ch1.current_phase = 0.0f;
    dpll_ch1.phase_error = 0.0f;
    dpll_ch1.is_locked = false;
    dpll_ch1.lock_counter = 0;
    
    dpll_ch2.target_freq = freq2;
    dpll_ch2.current_phase = 0.0f;
    dpll_ch2.phase_error = 0.0f;
    dpll_ch2.is_locked = false;
    dpll_ch2.lock_counter = 0;
    
    dpll_simple_enabled = true;
}

/**
 * @brief 简化相位检测
 */
static float DPLL_Simple_PhaseDetect(uint16_t *adc_data, uint16_t length, float freq) {
    float phase_sum = 0.0f;
    float sample_rate = 1000000.0f; // 1MSPS
    
    // 简化的相位检测算法
    for (uint16_t i = 0; i < length && i < 100; i++) { // 只处理前100个点
        float sample = ((float)adc_data[i] - 2048.0f) / 2048.0f;
        float ref_sin = arm_sin_f32(2.0f * M_PI * freq * i / sample_rate);
        phase_sum += sample * ref_sin;
    }
    
    return phase_sum / 100.0f;
}

/**
 * @brief 简化锁相环更新
 */
void DPLL_Simple_Update(uint16_t *adc_data, uint16_t length) {
    if (!dpll_simple_enabled) return;
    
    // 检测两路信号的相位
    float phase1 = DPLL_Simple_PhaseDetect(adc_data, length, dpll_ch1.target_freq);
    float phase2 = DPLL_Simple_PhaseDetect(adc_data, length, dpll_ch2.target_freq);
    
    // 计算相位误差
    dpll_ch1.phase_error = phase1 - dpll_ch1.current_phase;
    dpll_ch2.phase_error = phase2 - dpll_ch2.current_phase;
    
    // 简单的PI控制器
    const float kp = 0.1f;
    dpll_ch1.current_phase += kp * dpll_ch1.phase_error;
    dpll_ch2.current_phase += kp * dpll_ch2.phase_error;
    
    // 相位限制在[0, 2π]
    while (dpll_ch1.current_phase >= 2.0f * M_PI) {
        dpll_ch1.current_phase -= 2.0f * M_PI;
    }
    while (dpll_ch1.current_phase < 0.0f) {
        dpll_ch1.current_phase += 2.0f * M_PI;
    }
    
    while (dpll_ch2.current_phase >= 2.0f * M_PI) {
        dpll_ch2.current_phase -= 2.0f * M_PI;
    }
    while (dpll_ch2.current_phase < 0.0f) {
        dpll_ch2.current_phase += 2.0f * M_PI;
    }
    
    // 锁定检测
    if (fabsf(dpll_ch1.phase_error) < 0.1f) {
        dpll_ch1.lock_counter++;
        if (dpll_ch1.lock_counter > 10) {
            dpll_ch1.is_locked = true;
        }
    } else {
        dpll_ch1.lock_counter = 0;
        dpll_ch1.is_locked = false;
    }
    
    if (fabsf(dpll_ch2.phase_error) < 0.1f) {
        dpll_ch2.lock_counter++;
        if (dpll_ch2.lock_counter > 10) {
            dpll_ch2.is_locked = true;
        }
    } else {
        dpll_ch2.lock_counter = 0;
        dpll_ch2.is_locked = false;
    }
    
    // 应用相位校正到DDS
    DPLL_Simple_ApplyCorrection();
}

/**
 * @brief 应用相位校正
 */
void DPLL_Simple_ApplyCorrection(void) {
    // 将相位转换为AD9833的相位寄存器值
    uint16_t phase_reg1 = (uint16_t)(dpll_ch1.current_phase / (2.0f * M_PI) * 4096.0f);
    uint16_t phase_reg2 = (uint16_t)(dpll_ch2.current_phase / (2.0f * M_PI) * 4096.0f);
    
    // 应用到AD9833
    AD9833_SetPhase_No1(0, phase_reg1);
    AD9833_SetPhase_No2(0, phase_reg2);
}

/**
 * @brief 获取锁定状态
 */
bool DPLL_Simple_IsLocked(void) {
    return (dpll_ch1.is_locked && dpll_ch2.is_locked);
}

/**
 * @brief 获取相位误差（度）
 */
float DPLL_Simple_GetPhaseError(uint8_t channel) {
    if (channel == 0) {
        return dpll_ch1.phase_error * 180.0f / M_PI;
    } else {
        return dpll_ch2.phase_error * 180.0f / M_PI;
    }
}

/**
 * @brief 设置相位偏移
 */
void DPLL_Simple_SetPhaseOffset(uint8_t channel, float phase_deg) {
    float phase_rad = phase_deg * M_PI / 180.0f;
    
    if (channel == 0) {
        dpll_ch1.current_phase = phase_rad;
    } else {
        dpll_ch2.current_phase = phase_rad;
    }
    
    DPLL_Simple_ApplyCorrection();
}

/**
 * @brief 使能/禁用简化锁相环
 */
void DPLL_Simple_Enable(bool enable) {
    dpll_simple_enabled = enable;
}

/**
 * @brief 重置锁相环
 */
void DPLL_Simple_Reset(void) {
    dpll_ch1.current_phase = 0.0f;
    dpll_ch1.phase_error = 0.0f;
    dpll_ch1.is_locked = false;
    dpll_ch1.lock_counter = 0;
    
    dpll_ch2.current_phase = 0.0f;
    dpll_ch2.phase_error = 0.0f;
    dpll_ch2.is_locked = false;
    dpll_ch2.lock_counter = 0;
}
