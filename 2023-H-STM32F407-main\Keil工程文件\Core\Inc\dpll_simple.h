/**
 * @file dpll_simple.h
 * @brief 简化的数字锁相环头文件
 * <AUTHOR> Assistant
 * @date 2025
 */

#ifndef __DPLL_SIMPLE_H
#define __DPLL_SIMPLE_H

#include "stdint.h"
#include "stdbool.h"

// 函数声明
void DPLL_Simple_Init(float freq1, float freq2);
void DPLL_Simple_Update(uint16_t *adc_data, uint16_t length);
void DPLL_Simple_ApplyCorrection(void);
bool DPLL_Simple_IsLocked(void);
float DPLL_Simple_GetPhaseError(uint8_t channel);
void DPLL_Simple_SetPhaseOffset(uint8_t channel, float phase_deg);
void DPLL_Simple_Enable(bool enable);
void DPLL_Simple_Reset(void);

#endif /* __DPLL_SIMPLE_H */
