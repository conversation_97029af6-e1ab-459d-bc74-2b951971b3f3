#include "key.h"

// 定义按键结构体数组
struct Keys key[3] = {0, 0, 0, 0,0};
// 中断回调函数，用于按键状态采集，作为相位补偿的定时器
bool is_phase_compensation_done = false; //是否开始补偿相位
uint16_t tim2_cnt;
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)  //1ms
{
	is_phase_compensation_done = true;   //1ms的标志位
	
    if (htim->Instance == TIM2)
    {
			   // 读取按键当前状态
        key[0].key_sta = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_4);
        key[1].key_sta = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3);
        key[2].key_sta = HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_2);	
    }
			
	  tim2_cnt++;
		if(tim2_cnt%10==0)
		{
			tim2_cnt=0;
			for(uint8_t i=0; i<3;i++)
			{
			switch(key[i].judeg_sta)
			{
				case 0:
				{	
					if(key[i].key_sta==0)key[i].judeg_sta=1;
				}break;
				
				case 1:
				{	
					if(key[i].key_sta==0)
					{
						key[i].judeg_sta=2;
					}		
					else key[i].judeg_sta=0;  
				}break;
				
				
				case 2:    
				{	
					if(key[i].key_sta==1)
					{
						key[i].judeg_sta=0;
						if(key[i].key_time<40)key[i].short_flag=1;
						key[i].key_time=0;	
					}	
					
					else   
					{
						key[i].key_time++;
						if(key[i].key_time>30)
						{
							key[i].long_flag=1;
							key[i].key_time = 0;
						}
					}break;
				}
			}	
	}			
			
		}
}

// 相位设置函数
bool phase_set(uint16_t *phase)
{
    extern bool dpll_mode_enabled;

    if (key[0].long_flag == 1) // 确认按键
    {
        key[0].long_flag = 0;
        return true;
    }
    else if (key[1].long_flag == 1) // 减小按键
    {
        key[1].long_flag = 0;
        if (dpll_mode_enabled) {
            // 在DPLL模式下，调整A路相位偏移
            extern void DPLL_App_SetPhaseOffset(uint8_t channel, float phase_offset_deg);
            static float dpll_phase_offset = 0.0f;
            dpll_phase_offset -= 5.0f;
            if (dpll_phase_offset < 0.0f) dpll_phase_offset = 0.0f;
            DPLL_App_SetPhaseOffset(0, dpll_phase_offset);
        } else {
            *phase = (*phase >= 5) ? (*phase - 5) : 0; // 防止相位值下溢
        }
    }
    else if (key[2].long_flag == 1) // 增大按键
    {
        key[2].long_flag = 0;
        if (dpll_mode_enabled) {
            // 在DPLL模式下，调整A路相位偏移
            extern void DPLL_App_SetPhaseOffset(uint8_t channel, float phase_offset_deg);
            static float dpll_phase_offset = 0.0f;
            dpll_phase_offset += 5.0f;
            if (dpll_phase_offset > 180.0f) dpll_phase_offset = 180.0f;
            DPLL_App_SetPhaseOffset(0, dpll_phase_offset);
        } else {
            *phase = (*phase <= 175) ? (*phase + 5) : 180; // 限制最大相位值
        }
    }

    // 检查短按键，用于切换DPLL模式
    if (key[0].short_flag == 1) {
        key[0].short_flag = 0;
        dpll_mode_enabled = !dpll_mode_enabled;
        if (dpll_mode_enabled) {
            extern void DPLL_App_Init(void);
            DPLL_App_Init();
        } else {
            extern void DPLL_App_DeInit(void);
            DPLL_App_DeInit();
        }
    }

    return false;
}
