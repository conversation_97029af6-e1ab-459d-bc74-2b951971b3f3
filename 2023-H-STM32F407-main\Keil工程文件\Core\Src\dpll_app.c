/**
 * @file dpll_app.c
 * @brief 数字锁相环应用层实现
 * <AUTHOR> Assistant
 * @date 2025
 */

#include "dpll_app.h"
#include "calculate.h"
#include "AD9833.h"
#include "main.h"
#include "tim.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 全局变量
static DPLL_TypeDef dpll_A, dpll_B;  // 两路数字锁相环
static float input_buffer[DPLL_SAMPLE_LENGTH];  // 输入信号缓冲区
static bool dpll_enabled = false;
static uint32_t dpll_update_counter = 0;

// 锁相环状态
typedef enum {
    DPLL_STATE_INIT,
    DPLL_STATE_FREQ_DETECT,
    DPLL_STATE_PHASE_LOCK,
    DPLL_STATE_LOCKED
} DPLL_State_t;

static DPLL_State_t dpll_state = DPLL_STATE_INIT;

/**
 * @brief 数字锁相环应用初始化
 */
void DPLL_App_Init(void) {
    // 初始化两路锁相环，使用检测到的频率
    extern double frequency[2];
    
    DPLL_Init(&dpll_A, frequency[0]);
    DPLL_Init(&dpll_B, frequency[1]);
    
    dpll_enabled = true;
    dpll_state = DPLL_STATE_FREQ_DETECT;
    dpll_update_counter = 0;
}

/**
 * @brief 数字锁相环应用去初始化
 */
void DPLL_App_DeInit(void) {
    dpll_enabled = false;
    dpll_state = DPLL_STATE_INIT;
}

/**
 * @brief 将ADC数据转换为浮点数组
 * @param adc_data: ADC数据数组
 * @param float_data: 浮点数据数组
 * @param length: 数据长度
 */
static void ConvertADCToFloat(uint16_t *adc_data, float *float_data, uint16_t length) {
    const float adc_ref = 4096.0f;  // 12位ADC参考值
    const float dc_offset = 2048.0f;  // 直流偏置
    
    for (uint16_t i = 0; i < length; i++) {
        // 去除直流偏置并归一化
        float_data[i] = ((float)adc_data[i] - dc_offset) / dc_offset;
    }
}

/**
 * @brief 数字锁相环主处理函数
 * @param adc_buffer: ADC采样缓冲区
 * @param length: 缓冲区长度
 */
void DPLL_App_Process(uint16_t *adc_buffer, uint16_t length) {
    if (!dpll_enabled || length != DPLL_SAMPLE_LENGTH) {
        return;
    }
    
    // 转换ADC数据为浮点数
    ConvertADCToFloat(adc_buffer, input_buffer, length);
    
    switch (dpll_state) {
        case DPLL_STATE_INIT:
            // 初始化状态，等待频率检测完成
            break;
            
        case DPLL_STATE_FREQ_DETECT:
            // 频率检测状态，更新锁相环目标频率
            extern double frequency[2];
            dpll_A.input_freq = frequency[0];
            dpll_B.input_freq = frequency[1];
            dpll_state = DPLL_STATE_PHASE_LOCK;
            break;
            
        case DPLL_STATE_PHASE_LOCK:
            // 相位锁定状态
            DPLL_Update(&dpll_A, input_buffer, length);
            DPLL_Update(&dpll_B, input_buffer, length);
            
            // 检查是否锁定
            if (dpll_A.is_locked && dpll_B.is_locked) {
                dpll_state = DPLL_STATE_LOCKED;
            }
            
            // 应用相位校正到DDS
            DPLL_App_ApplyPhaseCorrection();
            break;
            
        case DPLL_STATE_LOCKED:
            // 锁定状态，继续维持锁定
            DPLL_Update(&dpll_A, input_buffer, length);
            DPLL_Update(&dpll_B, input_buffer, length);
            
            // 应用相位校正到DDS
            DPLL_App_ApplyPhaseCorrection();
            
            // 检查是否失锁
            if (!dpll_A.is_locked || !dpll_B.is_locked) {
                dpll_state = DPLL_STATE_PHASE_LOCK;
            }
            break;
    }
    
    dpll_update_counter++;
}

/**
 * @brief 应用相位校正到DDS芯片
 */
void DPLL_App_ApplyPhaseCorrection(void) {
    // 计算相位校正值（转换为AD9833的相位寄存器值）
    uint16_t phase_reg_A = (uint16_t)(dpll_A.phase_correction / (2.0f * M_PI) * 4096.0f);
    uint16_t phase_reg_B = (uint16_t)(dpll_B.phase_correction / (2.0f * M_PI) * 4096.0f);
    
    // 应用到AD9833
    AD9833_SetPhase_No1(0, phase_reg_A);
    AD9833_SetPhase_No2(0, phase_reg_B);
    
    // 也可以通过调整定时器来实现更精确的相位控制
    uint32_t timer_delay_A = DPLL_GetTimerDelay(dpll_A.phase_correction, dpll_A.output_freq, 168000000);
    uint32_t timer_delay_B = DPLL_GetTimerDelay(dpll_B.phase_correction, dpll_B.output_freq, 168000000);
    
    // 这里可以调整定时器的计数值来实现精确的相位控制
    // 具体实现取决于你的硬件配置
}

/**
 * @brief 获取锁相环状态信息
 * @param info: 状态信息结构体指针
 */
void DPLL_App_GetStatus(DPLL_Status_t *info) {
    info->state = dpll_state;
    info->is_enabled = dpll_enabled;
    info->update_counter = dpll_update_counter;
    
    // A路信息
    info->dpll_A.frequency = dpll_A.output_freq;
    info->dpll_A.phase_error_deg = DPLL_RadToDeg(dpll_A.phase_error);
    info->dpll_A.phase_correction_deg = DPLL_RadToDeg(dpll_A.phase_correction);
    info->dpll_A.frequency_error = dpll_A.frequency_error;
    info->dpll_A.is_locked = dpll_A.is_locked;
    
    // B路信息
    info->dpll_B.frequency = dpll_B.output_freq;
    info->dpll_B.phase_error_deg = DPLL_RadToDeg(dpll_B.phase_error);
    info->dpll_B.phase_correction_deg = DPLL_RadToDeg(dpll_B.phase_correction);
    info->dpll_B.frequency_error = dpll_B.frequency_error;
    info->dpll_B.is_locked = dpll_B.is_locked;
}

/**
 * @brief 设置锁相环参数
 * @param channel: 通道选择 (0=A, 1=B)
 * @param target_freq: 目标频率
 */
void DPLL_App_SetTargetFreq(uint8_t channel, float target_freq) {
    if (channel == 0) {
        dpll_A.input_freq = target_freq;
        dpll_A.output_freq = target_freq;
    } else if (channel == 1) {
        dpll_B.input_freq = target_freq;
        dpll_B.output_freq = target_freq;
    }
}

/**
 * @brief 手动设置相位偏移
 * @param channel: 通道选择 (0=A, 1=B)
 * @param phase_offset_deg: 相位偏移（度）
 */
void DPLL_App_SetPhaseOffset(uint8_t channel, float phase_offset_deg) {
    float phase_offset_rad = DPLL_DegToRad(phase_offset_deg);
    
    if (channel == 0) {
        dpll_A.phase_correction = phase_offset_rad;
    } else if (channel == 1) {
        dpll_B.phase_correction = phase_offset_rad;
    }
    
    // 立即应用相位校正
    DPLL_App_ApplyPhaseCorrection();
}

/**
 * @brief 重置锁相环
 * @param channel: 通道选择 (0=A, 1=B, 2=Both)
 */
void DPLL_App_Reset(uint8_t channel) {
    if (channel == 0 || channel == 2) {
        DPLL_Init(&dpll_A, dpll_A.input_freq);
    }
    if (channel == 1 || channel == 2) {
        DPLL_Init(&dpll_B, dpll_B.input_freq);
    }
    
    if (channel == 2) {
        dpll_state = DPLL_STATE_FREQ_DETECT;
    }
}

/**
 * @brief 获取锁相环是否锁定
 * @return 锁定状态
 */
bool DPLL_App_IsLocked(void) {
    return (dpll_A.is_locked && dpll_B.is_locked);
}
