#ifndef _AD9833_H_
#define _AD9833_H_

#include "main.h"
//引脚定义
#define FSYNC_1_No1 				HAL_GPIO_WritePin(GPIOF, GPIO_PIN_2, GPIO_PIN_SET)
#define FSYNC_0_No1   	   	HAL_GPIO_WritePin(GPIOF, GPIO_PIN_2, GPIO_PIN_RESET)

#define SCLK_1_No1 	    	  HAL_GPIO_WritePin(GPIOF, GPIO_PIN_3, GPIO_PIN_SET)
#define SCLK_0_No1 	    	  HAL_GPIO_WritePin(GPIOF, GPIO_PIN_3, GPIO_PIN_RESET)

#define SDATA_1_No1 				HAL_GPIO_WritePin(GPIOF, GP<PERSON>_PIN_4, GP<PERSON>_PIN_SET)
#define SDATA_0_No1 	  		HAL_GPIO_WritePin(GPIOF, GPIO_PIN_4, GPIO_PIN_RESET)

#define FSYNC_1_No2 				HAL_GPIO_WritePin(GPIOF, GPIO_PIN_5, <PERSON><PERSON>_PIN_SET)
#define FSYNC_0_No2   	   	HAL_GPIO_WritePin(GPIOF, GPIO_PIN_5, GPIO_PIN_RESET)

#define SCLK_1_No2 	    	  HAL_GPIO_WritePin(GPIOF, GPIO_PIN_6, GPIO_PIN_SET)
#define SCLK_0_No2 	    	  HAL_GPIO_WritePin(GPIOF, GPIO_PIN_6, GPIO_PIN_RESET)

#define SDATA_1_No2 				HAL_GPIO_WritePin(GPIOF, GPIO_PIN_7, GPIO_PIN_SET)
#define SDATA_0_No2 	  		HAL_GPIO_WritePin(GPIOF, GPIO_PIN_7, GPIO_PIN_RESET)

/* WaveMode */
#define AD9833_OUT_SINUS    ((0 << 5) | (0 << 1) | (0 << 3))
#define AD9833_OUT_TRIANGLE ((0 << 5) | (1 << 1) | (0 << 3))
#define AD9833_OUT_MSB      ((1 << 5) | (0 << 1) | (1 << 3))
#define AD9833_OUT_MSB2     ((1 << 5) | (0 << 1) | (0 << 3))

/* Registers */
#define AD9833_REG_CMD      (0 << 14)
#define AD9833_REG_FREQ0    (1 << 14)
#define AD9833_REG_FREQ1    (2 << 14)
#define AD9833_REG_PHASE0   (6 << 13)
#define AD9833_REG_PHASE1   (7 << 13)

/* Command Control Bits */
#define AD9833_B28          (1 << 13)
#define AD9833_HLB          (1 << 12)
#define AD9833_FSEL0        (0 << 11)
#define AD9833_FSEL1        (1 << 11)
#define AD9833_PSEL0        (0 << 10)
#define AD9833_PSEL1        (1 << 10)
#define AD9833_PIN_SW       (1 << 9)
#define AD9833_RESET        (1 << 8)
#define AD9833_CLEAR_RESET  (0 << 8)
#define AD9833_SLEEP1       (1 << 7)
#define AD9833_SLEEP12      (1 << 6)
#define AD9833_OPBITEN      (1 << 5)
#define AD9833_SIGN_PIB     (1 << 4)
#define AD9833_DIV2         (1 << 3)
#define AD9833_MODE         (1 << 1)

void AD9833_WriteData_No1(u16 txdata);
void AD9833_WriteData_No2(u16 txdata);

void AD9833_SetFrequency_No1(unsigned short reg, double fout);
void AD9833_SetFrequency_No2(unsigned short reg, double fout);

void AD9833_SetPhase_No1(unsigned short reg, unsigned short val);
void AD9833_SetPhase_No2(unsigned short reg, unsigned short val);

void AD9833_SetWave_No1(unsigned int WaveMode,unsigned int Freq_SFR,unsigned int Phase_SFR);
void AD9833_SetWave_No2(unsigned int WaveMode,unsigned int Freq_SFR,unsigned int Phase_SFR);

void AD9833_Setup_No1(unsigned int Freq_SFR,double Freq,unsigned int Phase_SFR,unsigned int Phase,unsigned int WaveMode);
void AD9833_Setup_No2(unsigned int Freq_SFR,double Freq,unsigned int Phase_SFR,unsigned int Phase,unsigned int WaveMode);

#endif 
