/**
 * @file dpll_app.h
 * @brief 数字锁相环应用层头文件
 * <AUTHOR> Assistant
 * @date 2025
 */

#ifndef __DPLL_APP_H
#define __DPLL_APP_H

#include "stdint.h"
#include "stdbool.h"
#include "calculate.h"

// 锁相环状态枚举
typedef enum {
    DPLL_STATE_INIT,
    DPLL_STATE_FREQ_DETECT,
    DPLL_STATE_PHASE_LOCK,
    DPLL_STATE_LOCKED
} DPLL_State_t;

// 单路锁相环状态信息
typedef struct {
    float frequency;              // 当前频率
    float phase_error_deg;        // 相位误差（度）
    float phase_correction_deg;   // 相位校正值（度）
    float frequency_error;        // 频率误差
    bool is_locked;              // 锁定状态
} DPLL_ChannelStatus_t;

// 锁相环整体状态信息
typedef struct {
    DPLL_State_t state;          // 当前状态
    bool is_enabled;             // 使能状态
    uint32_t update_counter;     // 更新计数器
    DPLL_ChannelStatus_t dpll_A; // A路状态
    DPLL_ChannelStatus_t dpll_B; // B路状态
} DPLL_Status_t;

// 函数声明
void DPLL_App_Init(void);
void DPLL_App_DeInit(void);
void DPLL_App_Process(uint16_t *adc_buffer, uint16_t length);
void DPLL_App_ApplyPhaseCorrection(void);
void DPLL_App_GetStatus(DPLL_Status_t *info);
void DPLL_App_SetTargetFreq(uint8_t channel, float target_freq);
void DPLL_App_SetPhaseOffset(uint8_t channel, float phase_offset_deg);
void DPLL_App_Reset(uint8_t channel);
bool DPLL_App_IsLocked(void);

#endif /* __DPLL_APP_H */
