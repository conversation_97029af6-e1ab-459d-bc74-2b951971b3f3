/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"
#include "fsmc.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "lcd.h"
#include "stdio.h"
#include "stdbool.h"
#include "arm_const_structs.h"
#include "arm_math.h"
#include "calculate.h"
#include "AD9833.h"
#include "SI5351A.h"
#include "Relays.h"
#include "key.h"
#include "dpll_app.h"

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
#define FFT_LENGTH_C         1024	        //采样点数
#define F_RESOLUTION_C       1.000381     //频率分辨率  时钟频率分频168/164M

#define MEAN_FILTER_NUM      30           //均值滤波的次数
#define PHASE_BIAS           5           //相位差的偏移
#define TIM3_FREQ            10000.0      //测量频差的时钟频率

#define FREQ_OFFSET          10            //固定频率差值
#define MEDIAN_FILTER        10           //测量频差中值滤波器的长度

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
/*==================================变量定义==================================*/
bool do_ad_flagc = false;    					  //开启ADC转换的标志位 测量C信号
bool is_C_judge_done = false;           //数据判断完成了，也就是只判断一次

uint16_t adc_buf_C[FFT_LENGTH_C];       //存储C的FFT

float FFT_Input[FFT_LENGTH_C*2] = {0};  //FFT输入
float FFT_Output[FFT_LENGTH_C] = {0};   //FFT输入

uint16_t max_index;
uint16_t second_max_index;

uint8_t wave_type[2];     					  //分别C中的两种波形
float mag_1harmo[2],mag_3harmo[2],mag_5harmo[2];   //谐波分量的
float sum_mag_1harmo[2],sum_mag_3harmo[2],sum_mag_5harmo[2];
float ave_mag_1harmo[2],ave_mag_3harmo[2],ave_mag_5harmo[2];
uint8_t mean_filter_cnt = 0;          //均值滤波计数

double  frequency[2];      		 			   	//C信号中的两种频率
double  frequency_bias;      		 		    //A信号的频差
uint16_t capture_cnt_A[MEDIAN_FILTER];  //对捕获值计数
bool is_capture_A_done = false;         //捕获是否完成，频率补偿一次标志位

extern bool is_phase_compensation_done; //是否开始补偿相位

uint16_t m_phase;											//定义相位差
uint8_t Lcd_String[20];     					//LCD输出的字符串

// 数字锁相环相关变量
DPLL_Status_t dpll_status;
bool dpll_mode_enabled = false;          // 数字锁相环模式使能

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */


/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_FSMC_Init();
  MX_USART1_UART_Init();
  MX_ADC1_Init();
  MX_TIM8_Init();
  MX_TIM3_Init();
  MX_TIM2_Init();
  MX_ADC3_Init();
  /* USER CODE BEGIN 2 */
	/*============================= 模块初始化 ==================================*/
	lcd_init(); 
	
  HAL_TIM_Base_Start(&htim8);      //开启C信号的采样定时器
	HAL_TIM_Base_Start_IT(&htim2);      //开启普通中断定时器,用于按键延时
  HAL_TIM_IC_Start_IT(&htim3,TIM_CHANNEL_1);  //开始测量频差的定时器


	HAL_ADC_Start_DMA(&hadc1, (uint32_t*)&adc_buf_C, FFT_LENGTH_C); //开始ADC转换
	
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
		
		/*---------------------判断并识别C信号-----------------------*/				
		if(do_ad_flagc && (!is_C_judge_done))  //DMA传输完成，开始分析数据，只进来一次
 		{
				for(uint16_t i=0; i<FFT_LENGTH_C; i++){
					FFT_Input[i*2] = adc_buf_C[i];  
					FFT_Input[i*2+1] = 0;           
				}				
				arm_cfft_f32(&arm_cfft_sR_f32_len1024, FFT_Input, 0, 1);  
				arm_cmplx_mag_f32(FFT_Input, FFT_Output, FFT_LENGTH_C);     		
				float max_magnitude = 0;  
				float second_max_magnitude = 0;				
				for(uint16_t i = 1; i < 300; i++) {
						if(FFT_Output[i] > max_magnitude) {
								second_max_magnitude = max_magnitude;
								second_max_index = max_index;
								max_magnitude = FFT_Output[i];
								max_index = i;
						}
						else if (FFT_Output[i] > second_max_magnitude) {
								second_max_magnitude = FFT_Output[i];
								second_max_index = i;
						}						
				}						
				if(second_max_index > max_index)  {
						uint16_t temp = second_max_index;
					  second_max_index =  max_index;
					  max_index = temp;
				}		
				
				frequency[0] = round(second_max_index * F_RESOLUTION_C );	      
				frequency[1] = round(max_index * F_RESOLUTION_C );	
								
				mag_1harmo[0] = FFT_Output[second_max_index-1] +  FFT_Output[second_max_index] +  FFT_Output[second_max_index+1];  
				mag_1harmo[1] = FFT_Output[max_index-1] +  FFT_Output[max_index] +  FFT_Output[max_index+1];   				
				mag_3harmo[0] = FFT_Output[second_max_index*3-1] +  FFT_Output[second_max_index*3] +  FFT_Output[second_max_index*3+1]; 
				mag_3harmo[1] = FFT_Output[max_index*3-1] +  FFT_Output[max_index*3] +  FFT_Output[max_index*3+1];  								
				mag_5harmo[0] = FFT_Output[second_max_index*5-1] +  FFT_Output[second_max_index*5] +  FFT_Output[second_max_index*5+1]; 
				mag_5harmo[1] = FFT_Output[max_index*5-1] +  FFT_Output[max_index*5] +  FFT_Output[max_index*5+1]; 
					
				//均值滤波器处理数据
				mean_filter_cnt ++;   				
				sum_mag_1harmo[0] += mag_1harmo[0];
				sum_mag_1harmo[1] += mag_1harmo[1];
				sum_mag_3harmo[0] += mag_3harmo[0];
				sum_mag_3harmo[1] += mag_3harmo[1];
				sum_mag_5harmo[0] += mag_5harmo[0];
				sum_mag_5harmo[1] += mag_5harmo[1];
						
				if(mean_filter_cnt == MEAN_FILTER_NUM){
						ave_mag_1harmo[0] = sum_mag_1harmo[0]/MEAN_FILTER_NUM;   //小频率波的基波幅值
						ave_mag_1harmo[1] = sum_mag_1harmo[1]/MEAN_FILTER_NUM;   //第二个波
						ave_mag_3harmo[0] = sum_mag_3harmo[0]/MEAN_FILTER_NUM;   //小频率波的基波幅值
						ave_mag_3harmo[1] = sum_mag_3harmo[1]/MEAN_FILTER_NUM;   //第二个波
						ave_mag_5harmo[0] = sum_mag_5harmo[0]/MEAN_FILTER_NUM;   //小频率波的基波幅值
						ave_mag_5harmo[1] = sum_mag_5harmo[1]/MEAN_FILTER_NUM;   //第二个波				
										
						mean_filter_cnt=0;     //清除均值滤波器的计数
						
						if(frequency[0]*3 == frequency[1]) //优先判断三倍关系
						{  
								if(ave_mag_1harmo[0]/ave_mag_5harmo[0] > 100)  //第一个波是正弦波
								{
										wave_type[0] = SIN_WAVE;   
										if(ave_mag_1harmo[1]/ave_mag_3harmo[1] > 20)wave_type[1] = SIN_WAVE;   		//正弦波
										else if(ave_mag_1harmo[1]/ave_mag_3harmo[1] < 20)wave_type[1] = TRI_WAVE;  //三角波		
								}
								else if(ave_mag_1harmo[0]/ave_mag_5harmo[0] < 25)   //第一个波是三角波
								{
										wave_type[0] = TRI_WAVE;  //三角波  
										if(ave_mag_5harmo[1]>10000)wave_type[1] = TRI_WAVE;  //三角波
										else wave_type[1] = SIN_WAVE;   		//正弦波
								}						
						}	
						
						else{ 	//其他情况
								if(ave_mag_1harmo[0]/ave_mag_3harmo[0] > 20)wave_type[0] = SIN_WAVE;   		//正弦波
								else if(ave_mag_1harmo[0]/ave_mag_3harmo[0] < 20)wave_type[0] = TRI_WAVE;  //三角波
								if(ave_mag_1harmo[1]/ave_mag_3harmo[1] > 20)wave_type[1] = SIN_WAVE;   		//正弦波
								else if(ave_mag_1harmo[1]/ave_mag_3harmo[1] < 20)wave_type[1] = TRI_WAVE;  //三角波								
						}
	
						sum_mag_1harmo[0]=0;  sum_mag_1harmo[1]=0;
						sum_mag_3harmo[0]=0;  sum_mag_3harmo[1]=0;
						sum_mag_5harmo[0]=0;  sum_mag_5harmo[1]=0;	  
						
						is_C_judge_done = true;     //C的信号判断完成了，紧接着生成波形
						HAL_TIM_Base_Stop(&htim8);   //停止C信号采样的时钟

						AD9833_WaveSeting_No1(frequency[0]*165,0,wave_type[0], 0);//2π/4096 × PHASEREG  1024  90
						AD9833_WaveSeting_No2(frequency[1]*165,0,wave_type[1], 0);//2π/4096 × PHASEREG  1024  90
						si5351aSetFrequency(4096000,1); //设置时钟为4.096MHz，避免量化误差

						// 初始化数字锁相环
						DPLL_App_Init();
				}			
				do_ad_flagc= false;    	        //清除标志位				
  	}

		/*--------------------- 计算，补偿频差 -----------------------*/		
		if(is_capture_A_done && is_C_judge_done)           //完成十次输入捕获并做中值滤波，只触发一次
		{
				uint16_t median_num = modeFilter(capture_cnt_A, sizeof(capture_cnt_A)/sizeof(uint16_t)); //做中值滤波器
				frequency_bias = TIM3_FREQ/median_num;         //计算A的频差
				is_capture_A_done = false;									   //清空标志位
			  AD9833_WaveSeting_No1(frequency[0]*1000 - frequency_bias,0,wave_type[0], 0); //2π/4096 × PHASEREG  1024  90					
				AD9833_WaveSeting_No2((frequency[1]*1.0/frequency[0])*(frequency[0]*1000 - frequency_bias),0,wave_type[1], 0); 
			//2π/4096 × PHASEREG  1024  90									
		}

		/*--------------------- 数字锁相环处理 -----------------------*/
		if(is_C_judge_done && dpll_mode_enabled) {
			// 使用数字锁相环进行相位锁定
			DPLL_App_Process(adc_buf_C, FFT_LENGTH_C > 1000 ? 1000 : FFT_LENGTH_C);

			// 获取锁相环状态
			DPLL_App_GetStatus(&dpll_status);
		}

		/*--------------------- 计算，补偿相差 -----------------------*/
//		if(!is_capture_A_done && is_phase_compensation_done)  //相位补偿完成后
//		{
//				is_phase_compensation_done = false;      //清空标志位
//				AD9833_SetPhase_No1(0,(unsigned int)1/360.0*4096);
//				AD9833_SetPhase_No2(0,(unsigned int)1/360.0*4096/(frequency[1]*1.0/frequency[0]));
//		}
		
		
		/*---------------------相位设置完成波形输出-----------------------*/		
		if(is_C_judge_done && phase_set(&m_phase))   //C信号判断完成
		{
			AD9833_WaveSeting_No1(frequency[0]*1000 - frequency_bias ,0,SIN_WAVE,0);//2π/4096 × PHASEREG  1024  90			
			AD9833_WaveSeting_No2((frequency[1]*1.0/frequency[0])*(frequency[0]*1000 -
					frequency_bias) ,0,SIN_WAVE, (unsigned int)(m_phase/360.0*4096 + PHASE_BIAS));//2π/4096 × PHASEREG  1024  90
		
			AD9833_Write_No1(0x0100); 			//复位AD9833,即RESET位为1
			AD9833_Write_No2(0x0100); 			//复位AD9833,即RESET位为1			
			HAL_Delay(50);
			si5351aSetFrequency(4096000,1); //设置时钟为4.096MHz，避免量化误差 			
			AD9833_Write_No1(0x0000); 			//复位AD9833,即RESET位为0
			AD9833_Write_No2(0x0000); 			//复位AD9833,即RESET位为0					
		}
			
		/*--------------------------LCD显示-------------------------------*/		
		lcd_show_string(10, 40, 240, 32, 32, "2023H", BLACK);
	      
		sprintf((char *)Lcd_String, "Freq1: %03d kHz", (int)frequency[0]); 
		lcd_show_string(10, 80, 240, 24, 24, (char *)Lcd_String, BLUE); 
		
  	sprintf((char *)Lcd_String, "Freq2: %03d kH z", (int)frequency[1]);
		lcd_show_string(10, 160, 240, 24, 24, (char *)Lcd_String, RED); 
		
		if(wave_type[0] == SIN_WAVE)sprintf((char *)Lcd_String, "Type1:Sine_Wave    ");
		else if(wave_type[0] == TRI_WAVE)sprintf((char *)Lcd_String, "Type1:Triangle_Wave");       
		lcd_show_string(10, 120, 240, 24, 24, (char *)Lcd_String, BLUE);  
		
		if(wave_type[1] == SIN_WAVE)sprintf((char *)Lcd_String, "Type2:Sine_Wave    ");
		else if(wave_type[1] == TRI_WAVE) sprintf((char *)Lcd_String, "Type2:Triangle_Wave");
		lcd_show_string(10, 200, 240, 24, 24, (char *)Lcd_String, RED); 
		
		sprintf((char *)Lcd_String, "Phase: %04d", m_phase); //单位是°   m_phase/360*4096
		lcd_show_string(10, 240, 240, 24, 24, (char *)Lcd_String, BLUE);
		
		sprintf((char *)Lcd_String, "Fre1Bias: %fHz", frequency_bias);
		lcd_show_string(10, 280, 240, 24, 24, (char *)Lcd_String, BLUE);

		// 数字锁相环状态显示
		if(dpll_mode_enabled) {
			sprintf((char *)Lcd_String, "DPLL: %s", DPLL_App_IsLocked() ? "LOCKED" : "UNLOCK");
			lcd_show_string(10, 320, 240, 24, 24, (char *)Lcd_String, GREEN);

			sprintf((char *)Lcd_String, "PhErr: %.1f°", dpll_status.dpll_A.phase_error_deg);
			lcd_show_string(10, 360, 240, 24, 24, (char *)Lcd_String, BLUE);
		}

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 336;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
//------------------------ TIM中断 ---------------------------/
int capture_A_index;
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim)
{
	if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1)   //注意这里为HAL_TIM_ACTIVE_CHANNEL_1而不是TIM_CHANNEL_1
	{
	  capture_cnt_A[capture_A_index++] = HAL_TIM_ReadCapturedValue(&htim3,TIM_CHANNEL_1);		
		if(capture_A_index == MEDIAN_FILTER)          //数据收集完成
		{
			capture_A_index = 0;
			is_capture_A_done = true;                   //数据收集完成
		}
	}
}

//------------------------ ADC中断 --------------------------/
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    if(hadc->Instance == ADC1) 
		{
				do_ad_flagc= true;
    }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
