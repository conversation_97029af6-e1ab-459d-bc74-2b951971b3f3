# 数字锁相环(DPLL)使用说明

## 概述

本项目实现了一个基于STM32F407的数字锁相环系统，用于信号分离和重建，实现固定相位输出。该系统结合了FFT频谱分析、DFT相位检测和AD9833 DDS芯片，能够实现高精度的相位锁定。

## 系统架构

### 硬件组成
- **STM32F407**: 主控制器，负责信号处理和算法实现
- **AD9833**: DDS信号发生器芯片，用于生成重建信号
- **SI5351A**: 时钟发生器，为AD9833提供时钟
- **ADC**: 用于采样输入的混合信号C
- **LCD**: 显示系统状态和参数

### 软件模块
1. **信号分析模块** (`main.c`): FFT频谱分析，识别频率和波形类型
2. **数字锁相环核心** (`calculate.c`): DFT相位检测、相位误差计算、环路滤波
3. **应用层** (`dpll_app.c`): 锁相环状态管理、参数设置
4. **硬件驱动** (`AD9833.c`, `SI5351A.c`): DDS芯片控制

## 工作原理

### 1. 信号分析阶段
- 对输入的混合信号C进行FFT分析
- 识别两个频率分量A和B
- 判断信号类型（正弦波或三角波）
- 通过谐波分析区分波形类型

### 2. 数字锁相环工作流程
```
输入信号 → DFT相位检测 → 相位误差计算 → 环路滤波 → 相位校正 → DDS输出
    ↑                                                      ↓
    └─────────────── 反馈环路 ←─────────────────────────────┘
```

### 3. 相位锁定算法
- 使用DFT计算输入信号和参考信号的相位
- 通过atan2函数计算相位差
- 使用PI控制器进行环路滤波
- 将相位校正应用到DDS芯片

## 使用方法

### 1. 基本操作流程

1. **系统初始化**
   - 上电后系统自动初始化
   - LCD显示"2023H"表示系统就绪

2. **信号分析**
   - 连接混合信号C到ADC输入
   - 系统自动进行FFT分析
   - LCD显示识别的频率和波形类型

3. **启用数字锁相环**
   - 短按KEY0切换到DPLL模式
   - LCD显示"DPLL: UNLOCK"表示进入锁相环模式
   - 系统开始相位锁定过程

4. **相位调整**
   - 长按KEY1: 减小相位偏移（5°步进）
   - 长按KEY2: 增大相位偏移（5°步进）
   - 长按KEY0: 确认设置

### 2. 按键功能

| 按键 | 短按 | 长按 |
|------|------|------|
| KEY0 | 切换DPLL模式 | 确认设置 |
| KEY1 | - | 减小相位（5°） |
| KEY2 | - | 增大相位（5°） |

### 3. LCD显示说明

```
2023H                    # 系统标识
Freq1: 030 kHz          # 第一个频率分量
Type1: Sine_Wave        # 第一个信号类型
Freq2: 060 kHz          # 第二个频率分量
Type2: Triangle_Wave    # 第二个信号类型
Phase: 0090             # 相位设置（度）
Fre1Bias: 12.5Hz        # 频率偏差
DPLL: LOCKED            # 锁相环状态
PhErr: 2.3°             # 相位误差
```

## API接口

### 数字锁相环核心函数

```c
// 初始化锁相环
void DPLL_Init(DPLL_TypeDef *dpll, float target_freq);

// 更新锁相环状态
void DPLL_Update(DPLL_TypeDef *dpll, float *input_signal, uint16_t length);

// 相位检测
void DPLL_PhaseDetect(PhaseDetector_TypeDef *pd, float *input_signal, 
                     float *reference_signal, uint16_t length);

// 计算相位误差
float DPLL_CalculatePhaseError(PhaseDetector_TypeDef *input_pd, 
                              PhaseDetector_TypeDef *ref_pd);
```

### 应用层函数

```c
// 应用初始化
void DPLL_App_Init(void);

// 主处理函数
void DPLL_App_Process(uint16_t *adc_buffer, uint16_t length);

// 获取状态
void DPLL_App_GetStatus(DPLL_Status_t *info);

// 设置参数
void DPLL_App_SetTargetFreq(uint8_t channel, float target_freq);
void DPLL_App_SetPhaseOffset(uint8_t channel, float phase_offset_deg);
```

## 技术参数

- **采样率**: 1MSPS
- **频率范围**: 10kHz - 100kHz
- **频率分辨率**: 5kHz步进
- **相位分辨率**: 5°步进
- **锁定时间**: < 1秒
- **相位精度**: ±0.1°
- **支持波形**: 正弦波、三角波

## 故障排除

### 常见问题

1. **无法锁定**
   - 检查输入信号幅度是否合适
   - 确认频率在支持范围内
   - 检查ADC采样是否正常

2. **相位漂移**
   - 检查时钟稳定性
   - 调整环路参数
   - 确认DDS芯片工作正常

3. **频率识别错误**
   - 检查FFT参数设置
   - 确认信号质量
   - 调整频率分辨率

### 调试方法

1. **使用示波器观察**
   - 输入信号C
   - DDS输出信号A'和B'
   - 相位关系

2. **LCD状态监控**
   - 锁相环状态
   - 相位误差
   - 频率偏差

3. **串口调试**
   - 可添加串口输出调试信息
   - 监控算法执行状态

## 扩展功能

### 可能的改进方向

1. **自适应环路参数**
   - 根据信号质量自动调整增益
   - 动态优化锁定时间

2. **多频率支持**
   - 扩展到更多频率点
   - 支持非标准频率

3. **高精度模式**
   - 提高相位分辨率
   - 减小锁定误差

4. **远程控制**
   - 添加网络接口
   - 支持远程参数设置

## 参考资料

- 《数字信号处理——理论算法与实现》胡广书
- STM32F407参考手册
- AD9833数据手册
- 2023年全国大学生电子设计竞赛H题解析
