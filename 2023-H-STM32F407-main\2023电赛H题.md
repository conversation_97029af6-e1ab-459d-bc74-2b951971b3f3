# 2023年电赛H题训练

## 解题思路参考博客
https://www.cnblogs.com/helesheng/p/17888312.html

## 使用stm32实现数字信号处理
ARM库的DSP库添加与使用
https://www.cnblogs.com/helesheng/p/15671138.html

## 利用定时器配置可控采用率的ADC
https://blog.csdn.net/xx201804/article/details/132839231?spm=1001.2014.3001.5501
https://blog.csdn.net/qq_42967008/article/details/89267010  （好用的定时器配置验证PWM）

## 利用定时器配置ADC(g3507)
https://blog.csdn.net/flmmmm/article/details/140571352?sharetype=blog&shareId=140571352&sharerefer=APP&sharesource=2301_77443610&sharefrom=qq

## STM32F407实现FFT，求频谱     
https://blog.csdn.net/xx201804/article/details/132889680

## DAC-DMA输出波形
https://www.cnblogs.com/lc-guo/p/17965475

## 如何使用Keil查看数组波形
https://blog.csdn.net/vbvcde/article/details/77890526

## 电赛知识点 - 帮助理解和用好DDS的一个在线工具
https://www.eet-china.com/mp/a65213.html
https://tools.analog.com/cn/simdds/?fin=1M&harmonicDB=-50&mult=1&part=AD9833&rso=111111&tof=1k&useFilters=0

## 很详细的AD8302芯片中文翻译资料
https://blog.csdn.net/runweipa/article/details/140070798#:~:text=AD8302%2C%E6%AF%8F%E4%B8%AA%E5%AF%B9%E6%95%B0%E6%94%BE%E5%A4%A7%E5%99%A8%E5%8F%AF%E4%BB%A5%E6%A3%80%E6%B5%8B%E8%BE%93%E5%85%A5%E4%BB%8E-73%E4%BC%8F%E7%89%B9%E5%88%86%E8%B4%9D%20%28223%CE%BCV%20-60%20dBm%20re%3A%2050%CE%A9%EF%BC%89%E5%88%B0-13%E4%BC%8F%E7%89%B9%E5%88%86%E8%B4%9D%20%28223,mV%2C%200%20dBm%20re%3A%2050%CE%A9%29%5D%E3%80%82%20%E6%B3%A8%E6%84%8F%EF%BC%8C%E5%AF%B9%E6%95%B0%E6%94%BE%E5%A4%A7%E5%99%A8%E5%AF%B9%E7%94%B5%E5%8E%8B%E8%80%8C%E4%B8%8D%E6%98%AF%E5%8A%9F%E7%8E%87%E6%9C%89%E5%93%8D%E5%BA%94%E3%80%82%20%E5%90%8C%E7%AD%89%E5%8A%9F%E7%8E%87%E8%83%BD%E6%8E%A8%E6%96%AD%E5%87%BA%E7%BB%99%E5%AE%9A%E4%B8%80%E4%B8%AA%E9%98%BB%E6%8A%97%E6%B0%B4%E5%B9%B3%2C%E4%BE%8B%E5%A6%82%2C%E5%B0%86%E4%BB%8E%E4%BC%8F%E7%89%B9%E5%88%86%E8%B4%9D%E5%88%B0dBm%E8%BD%AC%E6%8D%A2%E5%9C%A850%CE%A9%E7%B3%BB%E7%BB%9F%E4%B8%AD%2C%E5%8F%AA%E9%9C%80%E6%B7%BB%E5%8A%A013%E5%88%86%E8%B4%9D%E3%80%82%20%E4%B8%BA%E4%BA%86%E8%A6%86%E7%9B%96%E6%95%B4%E4%B8%AA%E8%8C%83%E5%9B%B4%EF%BC%8C%E6%9C%89%E5%BF%85%E8%A6%81%E5%AF%B9%E4%B8%80%E4%B8%AA%E5%AF%B9%E6%95%B0%E6%94%BE%E5%A4%A7%E5%99%A8%E5%BA%94%E7%94%A8%E4%B8%80%E4%B8%AA%E5%8F%82%E8%80%83%E7%94%B5%E5%B9%B3%EF%BC%8C%E8%AF%A5%E7%94%B5%E5%B9%B3%E6%81%B0%E5%A5%BD%E5%AF%B9%E5%BA%94%E4%BA%8E%E5%AE%83%E7%9A%84%E4%B8%AD%E7%A8%8B%E3%80%82

## AD9833调不通？看这篇文章就够了~
https://ez.analog.com/cn/other/f/forum/545070/ad9833
https://blog.csdn.net/ic2121/article/details/126879201

## AD9833介绍与应用（C语言实现）
https://blog.csdn.net/weixin_56608779/article/details/125654260

## AD630锁相环
https://blog.csdn.net/shaojie_45/article/details/115337141

## AD630使用教程
https://www.bilibili.com/opus/849423138224603157

## 完美解决STM32F407ZGT6使用Systic定时器实现延时
https://blog.csdn.net/qq_27826857/article/details/80724053

## SYSTEM文件夹下的delay.c文件内延时函数详解
https://blog.csdn.net/m0_62722416/article/details/126181663

## 最有用的一集
TM32开发中如何移植原子哥的system库到cubeMX生成的工程中（裸机）
https://blog.csdn.net/EE_Bee/article/details/106601907

## STM32 多通道ADC采集详解（DMA模式和非DMA模式）
https://blog.csdn.net/m0_38096844/article/details/83416405

## 关于多通道的理论补全
https://blog.csdn.net/m0_51994338/article/details/130754780

## 使用 STM32 测量频率和占空比的几种方法
https://www.cnblogs.com/SymPny/p/17569190.html

## 输入捕获测量频率（亲测 成功板）
https://blog.csdn.net/qq_62078117/article/details/124910112

## HAL库与Cubemx系列|Systick-系统滴答定时器详解
https://blog.csdn.net/qq_16519885/article/details/117756815

## 参考代码
正点原子STM32F407开发者探索板

## 理论补全
### 采样时间和转换时间
[STM32 HAL库][ADC]采样时间和转换时间
https://www.bilibili.com/video/BV1XM4m1y7eP/?share_source=copy_web&vd_source=3a6421bf6db85c6367e0ceaa4e2d5e0f

#### 采样时间（Sampling Time）
采样时间越长，采集信号与实际信号的误差就越小
采样深度为12时，分辨率 = (3.3-0)/(2^12-1) = 0.8mV
量程 = 0~3.3V，采样误差 < 1/4*分辨率 = 0.2mV，这样就不会对测量结果产生误差
最优采样时间公式：

\[ T_s = (R_{AIN} + R_{ADC}) \times C_{ADC} \times \ln(2^{N+2}) \]
其中 Ts是我们求的最优采样时间，Rain是模拟信号源的内阻，Radc是ADC采集保持电路的内阻，Cadc是采样保持电路的电容(8pF)，N是采样深度，使用的信号发生器的内阻Rain=50Ω
最后算出的最佳采样时间是 0.8148us，对应的ADC采样周期=Ts*fadc,fadc是由PCK2分频得到的ADC运行时钟频率。
最后在配置CubeMX的时候需要按照最接近的周期去选择。


#### 转换时间
STM32F407的采样深度是12，每尝试调整一位就会花掉一个ADC的时钟周期
转换时间 = 12Cycles + 0.5Cycles = 12.5Cycles
这个额外的0.5个周期时间是为了保证ADC能够准确地完成量化和编码过程，确保转换结果的准确性。这也是为什么在STM32的ADC配置中，转换时间总是比采样时间多0.5个周期的原因。

## 实现过程
### CubeMX配置DSP
参考一下两篇博客终于实现
首先使用CubeMX把DSP的库添加进来，不使用keil添加，可能是因为库的版本问题，我添加进来有八百多个报错，遂转Keil为CubeMX：
https://blog.csdn.net/qq_34022877/article/details/117855263

解决包含 arm_math.h、arm_const_structs.h 头文件时找不到头文件的问题：
在C/C++选项卡中,的Include Paths中添加DSP库的路径：\Drivers\CMSIS\DSP\Include
https://www.cnblogs.com/Godsheep/p/17350977.html

### 用到DSP的俩重要函数
```
void arm_cfft_f32    (
const arm_cfft_instance_f32 *     S,
float32_t *     p1,
uint8_t     ifftFlag,
uint8_t     bitReverseFlag 
);

arm_status arm_cmplx_mag_f32(
    const float32_t *pSrc,   // 输入复数数据的指针 (实部和虚部交替存储)
    float32_t *pDst,         // 输出幅值数据的指针
    uint32_t numSamples      // 输入样本的数量
);
```


## 鉴相器
同样的时钟源产生的两个信号的相位差与鉴相输出电压的关系

将两个通道采样的数据分别保存在两个数组中，可以通过 **DMA** 的双通道采样结合后续数据处理实现。以下是具体方法和代码示例：

---

### **基本原理**
1. **采样方式**  
   - 启用 ADC 的 **扫描模式**，按顺序采样两个通道（通道1和通道2）。
   - 启用 **DMA 模块**，将 ADC 转换结果存储到一个连续的缓冲区。

2. **数据分离**  
   - 将 DMA 采样缓冲区中的数据，按采样顺序分离到两个独立数组中。

---

### **实现步骤**

#### **1. 配置 ADC 和 DMA**
启用扫描模式，让 ADC 自动轮询采样两个通道的数据，并将采样值存储到 DMA 缓冲区。

#### **2. 定义全局缓冲区**
定义一个缓冲区用于存放 ADC 的采样结果，长度为 `2 × 数据点数`，因为每次采样两通道的数据。

#### **3. 数据分离处理**
在主循环中或通过 DMA 传输完成中断回调，将缓冲区的数据分离到两个独立的数组中。

---

### **完整代码示例（基于 STM32 HAL）**

#### **1. 定义全局变量**
```c
#define SAMPLE_POINTS 1024  // 每通道的采样点数

uint16_t adc_dma_buffer[SAMPLE_POINTS * 2]; // DMA缓冲区，用于存放两通道采样数据
uint16_t channel1_data[SAMPLE_POINTS];     // 通道1采样数据
uint16_t channel2_data[SAMPLE_POINTS];     // 通道2采样数据
```

#### **2. 初始化 ADC**
```c
void ADC_Init(void) {
    ADC_HandleTypeDef hadc1;
    ADC_ChannelConfTypeDef sConfig = {0};
    
    // ADC 基础配置
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B;
    hadc1.Init.ScanConvMode = ADC_SCAN_ENABLE;   // 启用扫描模式
    hadc1.Init.ContinuousConvMode = ENABLE;     // 连续转换模式
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc1.Init.NbrOfConversion = 2;             // 转换通道数量为 2
    HAL_ADC_Init(&hadc1);

    // 配置通道 1
    sConfig.Channel = ADC_CHANNEL_0;            // 第1通道
    sConfig.Rank = ADC_REGULAR_RANK_1;
    sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
    HAL_ADC_ConfigChannel(&hadc1, &sConfig);

    // 配置通道 2
    sConfig.Channel = ADC_CHANNEL_1;            // 第2通道
    sConfig.Rank = ADC_REGULAR_RANK_2;
    HAL_ADC_ConfigChannel(&hadc1, &sConfig);
}
```

#### **3. 配置 DMA**
```c
void DMA_Init(void) {
    static DMA_HandleTypeDef hdma_adc1;

    __HAL_RCC_DMA2_CLK_ENABLE(); // 启用DMA时钟

    hdma_adc1.Instance = DMA2_Stream0;
    hdma_adc1.Init.Channel = DMA_CHANNEL_0;
    hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY; // 数据从外设到内存
    hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;     // 外设地址不递增
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;         // 内存地址递增
    hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD; // 外设数据对齐
    hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;    // 内存数据对齐
    hdma_adc1.Init.Mode = DMA_CIRCULAR;              // 循环模式
    hdma_adc1.Init.Priority = DMA_PRIORITY_HIGH;

    HAL_DMA_Init(&hdma_adc1);

    // 链接DMA到ADC
    __HAL_LINKDMA(&hadc1, DMA_Handle, hdma_adc1);
}
```

#### **4. 启动 ADC 和 DMA**
```c
void Start_ADC(void) {
    // 开启 ADC 的 DMA 模式
    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_dma_buffer, SAMPLE_POINTS * 2);
}
```

#### **5. 数据分离**
在主循环或 DMA 完成回调函数中，将缓冲区中的数据分离到两个独立的数组中。

```c
void Process_ADC_Data(void) {
    for (uint16_t i = 0; i < SAMPLE_POINTS; i++) {
        channel1_data[i] = adc_dma_buffer[2 * i];     // 通道1的数据
        channel2_data[i] = adc_dma_buffer[2 * i + 1]; // 通道2的数据
    }
}
```

---

### **6. 主程序**
```c
int main(void) {
    HAL_Init();              // 初始化 HAL 库
    ADC_Init();              // 初始化 ADC
    DMA_Init();              // 初始化 DMA
    Start_ADC();             // 启动 ADC 和 DMA

    while (1) {
        Process_ADC_Data();  // 分离数据，存入独立数组
        HAL_Delay(100);      // 延时或其他处理逻辑
    }
}
```

---

### **注意事项**
1. **数据同步性**  
   - 因为 ADC 是逐通道采样，通道之间可能存在时间延迟。如果需要严格同步采样，考虑使用双 ADC 或硬件触发同步功能。

2. **数据处理优化**  
   - 如果 `SAMPLE_POINTS` 较大，可考虑用 **中断处理** 或 **定时器触发**，减少 CPU 的处理负担。

3. **DMA 缓冲区模式**  
   - 上述代码采用循环模式（**CIRCULAR**），适合连续采样。如果需要单次采样，改为 **NORMAL** 模式。

4. **采样速率和分辨率**  
   - 确保 ADC 的采样速率、分辨率和输入信号特性（频率、幅值）匹配，以防止信号失真或混叠。

需要更复杂的同步机制或硬件触发支持，可以告诉我你的具体芯片型号，我能进一步协助完善！
