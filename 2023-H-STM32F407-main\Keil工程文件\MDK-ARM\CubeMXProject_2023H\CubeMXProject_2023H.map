Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    lcd.o(i.lcd_clear) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_clear) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_clear) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_color_fill) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_color_fill) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_display_dir) refers to lcd.o(i.lcd_scan_dir) for lcd_scan_dir
    lcd.o(i.lcd_display_dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_display_off) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_display_off) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_display_on) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_display_on) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_draw_circle) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_draw_hline) refers to lcd.o(i.lcd_fill) for lcd_fill
    lcd.o(i.lcd_draw_hline) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_draw_line) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_draw_point) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_draw_point) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_draw_rectangle) refers to lcd.o(i.lcd_draw_line) for lcd_draw_line
    lcd.o(i.lcd_ex_ili9341_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ili9341_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ili9341_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_ili9806_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ili9806_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ili9806_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_nt35310_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_nt35310_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_nt35310_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_nt35510_reginit) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_ex_nt35510_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_nt35510_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_st7789_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_st7789_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_st7789_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_st7796_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_st7796_reginit) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_ex_st7796_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_fill) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_fill) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_fill_circle) refers to lcd.o(i.lcd_draw_hline) for lcd_draw_hline
    lcd.o(i.lcd_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_rd_data) for lcd_rd_data
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_init) refers to printfa.o(i.__0printf) for __2printf
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_st7789_reginit) for lcd_ex_st7789_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ili9341_reginit) for lcd_ex_ili9341_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_nt35310_reginit) for lcd_ex_nt35310_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_st7796_reginit) for lcd_ex_st7796_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_nt35510_reginit) for lcd_ex_nt35510_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ili9806_reginit) for lcd_ex_ili9806_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ssd1963_reginit) for lcd_ex_ssd1963_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ssd_backlight_set) for lcd_ssd_backlight_set
    lcd.o(i.lcd_init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_display_dir) for lcd_display_dir
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_clear) for lcd_clear
    lcd.o(i.lcd_init) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_rd_data) for lcd_rd_data
    lcd.o(i.lcd_read_point) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_scan_dir) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_set_cursor) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_set_cursor) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_set_cursor) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_set_window) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_set_window) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_set_window) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_show_char) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_show_char) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.lcd_show_char) refers to lcd.o(.data) for .data
    lcd.o(i.lcd_show_char) refers to lcd.o(.bss) for .bss
    lcd.o(i.lcd_show_num) refers to lcd.o(i.lcd_pow) for lcd_pow
    lcd.o(i.lcd_show_num) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_string) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_xnum) refers to lcd.o(i.lcd_pow) for lcd_pow
    lcd.o(i.lcd_show_xnum) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_ssd_backlight_set) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ssd_backlight_set) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ssd_backlight_set) refers to dfltui.o(.text) for __aeabi_ui2d
    lcd.o(i.lcd_ssd_backlight_set) refers to dmul.o(.text) for __aeabi_dmul
    lcd.o(i.lcd_ssd_backlight_set) refers to dfixui.o(.text) for __aeabi_d2uiz
    lcd.o(i.lcd_write_ram_prepare) refers to lcd.o(.bss) for .bss
    calculate.o(i.DFT_PhaseCalculation) refers to sqrtf_full.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    calculate.o(i.DFT_PhaseCalculation) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    calculate.o(i.DFT_SingleFreq) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    calculate.o(i.DFT_SingleFreq) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    calculate.o(i.DPLL_PhaseDetect) refers to calculate.o(i.DFT_PhaseCalculation) for DFT_PhaseCalculation
    calculate.o(i.DPLL_Update) refers to calculate.o(i.GenerateSinCosTable) for GenerateSinCosTable
    calculate.o(i.DPLL_Update) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    calculate.o(i.DPLL_Update) refers to calculate.o(i.DFT_SingleFreq) for DFT_SingleFreq
    calculate.o(i.DPLL_Update) refers to calculate.o(i.DFT_PhaseCalculation) for DFT_PhaseCalculation
    calculate.o(i.DPLL_Update) refers to calculate.o(i.DPLL_CalculatePhaseError) for DPLL_CalculatePhaseError
    calculate.o(i.DPLL_Update) refers to calculate.o(i.DPLL_FrequencyCorrection) for DPLL_FrequencyCorrection
    calculate.o(i.DPLL_Update) refers to calculate.o(i.DPLL_PhaseCorrection) for DPLL_PhaseCorrection
    calculate.o(i.DPLL_Update) refers to calculate.o(i.DPLL_IsLocked) for DPLL_IsLocked
    calculate.o(i.DPLL_Update) refers to calculate.o(.bss) for .bss
    calculate.o(i.DPLL_Update) refers to calculate.o(.data) for .data
    calculate.o(i.GenerateSinCosTable) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    calculate.o(i.GenerateSinCosTable) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    calculate.o(i.modeFilter) refers to malloc.o(i.malloc) for malloc
    calculate.o(i.modeFilter) refers to malloc.o(i.free) for free
    ad9833.o(i.AD9833_AmpSet_No1) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_AmpSet_No1) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833.o(i.AD9833_AmpSet_No2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_AmpSet_No2) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833.o(i.AD9833_SetFrequency_No1) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_SetFrequency_No1) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency_No1) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_SetFrequency_No1) refers to ad9833.o(i.AD9833_Write_No1) for AD9833_Write_No1
    ad9833.o(i.AD9833_SetFrequency_No2) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_SetFrequency_No2) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency_No2) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_SetFrequency_No2) refers to ad9833.o(i.AD9833_Write_No2) for AD9833_Write_No2
    ad9833.o(i.AD9833_SetPhase_No1) refers to ad9833.o(i.AD9833_Write_No1) for AD9833_Write_No1
    ad9833.o(i.AD9833_SetPhase_No2) refers to ad9833.o(i.AD9833_Write_No2) for AD9833_Write_No2
    ad9833.o(i.AD9833_WaveSeting_No1) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_WaveSeting_No1) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_WaveSeting_No1) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_WaveSeting_No1) refers to ad9833.o(i.AD9833_Write_No1) for AD9833_Write_No1
    ad9833.o(i.AD9833_WaveSeting_No2) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9833.o(i.AD9833_WaveSeting_No2) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_WaveSeting_No2) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9833.o(i.AD9833_WaveSeting_No2) refers to ad9833.o(i.AD9833_Write_No2) for AD9833_Write_No2
    ad9833.o(i.AD9833_Write_No1) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_Write_No1) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    ad9833.o(i.AD9833_Write_No2) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9833.o(i.AD9833_Write_No2) refers to ad9833.o(i.AD9833_Delay) for AD9833_Delay
    si5351a.o(i.I2C_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    si5351a.o(i.I2C_RecvByte) refers to si5351a.o(i.Delay_1us) for Delay_1us
    si5351a.o(i.I2C_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    si5351a.o(i.I2C_SendACK) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    si5351a.o(i.I2C_SendACK) refers to si5351a.o(i.Delay_1us) for Delay_1us
    si5351a.o(i.I2C_SendByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    si5351a.o(i.I2C_SendByte) refers to si5351a.o(i.Delay_1us) for Delay_1us
    si5351a.o(i.I2C_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    si5351a.o(i.I2C_Start) refers to si5351a.o(i.Delay_1us) for Delay_1us
    si5351a.o(i.I2C_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    si5351a.o(i.I2C_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    si5351a.o(i.I2C_Stop) refers to si5351a.o(i.Delay_1us) for Delay_1us
    si5351a.o(i.I2C_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    si5351a.o(i.I2C_WaitAck) refers to si5351a.o(i.Delay_1us) for Delay_1us
    si5351a.o(i.I2C_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    si5351a.o(i.Single_ReadI2C) refers to si5351a.o(i.I2C_Start) for I2C_Start
    si5351a.o(i.Single_ReadI2C) refers to si5351a.o(i.I2C_SendByte) for I2C_SendByte
    si5351a.o(i.Single_ReadI2C) refers to si5351a.o(i.I2C_WaitAck) for I2C_WaitAck
    si5351a.o(i.Single_ReadI2C) refers to si5351a.o(i.I2C_RecvByte) for I2C_RecvByte
    si5351a.o(i.Single_ReadI2C) refers to si5351a.o(i.I2C_SendACK) for I2C_SendACK
    si5351a.o(i.Single_ReadI2C) refers to si5351a.o(i.I2C_Stop) for I2C_Stop
    si5351a.o(i.Single_WriteI2C) refers to si5351a.o(i.I2C_Start) for I2C_Start
    si5351a.o(i.Single_WriteI2C) refers to si5351a.o(i.I2C_SendByte) for I2C_SendByte
    si5351a.o(i.Single_WriteI2C) refers to si5351a.o(i.I2C_WaitAck) for I2C_WaitAck
    si5351a.o(i.Single_WriteI2C) refers to si5351a.o(i.I2C_Stop) for I2C_Stop
    si5351a.o(i.i2cSendRegister) refers to si5351a.o(i.I2C_Start) for I2C_Start
    si5351a.o(i.i2cSendRegister) refers to si5351a.o(i.I2C_SendByte) for I2C_SendByte
    si5351a.o(i.i2cSendRegister) refers to si5351a.o(i.I2C_WaitAck) for I2C_WaitAck
    si5351a.o(i.i2cSendRegister) refers to si5351a.o(i.I2C_Stop) for I2C_Stop
    si5351a.o(i.setupMultisynth) refers to si5351a.o(i.i2cSendRegister) for i2cSendRegister
    si5351a.o(i.setupPLL) refers to si5351a.o(i.i2cSendRegister) for i2cSendRegister
    si5351a.o(i.si5351aSetFrequency) refers to si5351a.o(i.setupPLL) for setupPLL
    si5351a.o(i.si5351aSetFrequency) refers to si5351a.o(i.setupMultisynth) for setupMultisynth
    si5351a.o(i.si5351aSetFrequency) refers to si5351a.o(i.i2cSendRegister) for i2cSendRegister
    relays.o(i.Relays_Close_Set) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    relays.o(i.Relays_Open_Set) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    key.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.HAL_TIM_PeriodElapsedCallback) refers to key.o(.data) for .data
    key.o(i.HAL_TIM_PeriodElapsedCallback) refers to key.o(.bss) for .bss
    key.o(i.phase_set) refers to dpll_simple.o(i.DPLL_Simple_SetPhaseOffset) for DPLL_Simple_SetPhaseOffset
    key.o(i.phase_set) refers to d2f.o(.text) for __aeabi_d2f
    key.o(i.phase_set) refers to dpll_simple.o(i.DPLL_Simple_Init) for DPLL_Simple_Init
    key.o(i.phase_set) refers to dpll_simple.o(i.DPLL_Simple_Enable) for DPLL_Simple_Enable
    key.o(i.phase_set) refers to key.o(.bss) for .bss
    key.o(i.phase_set) refers to main.o(.data) for dpll_mode_enabled
    key.o(i.phase_set) refers to key.o(.data) for .data
    key.o(i.phase_set) refers to main.o(.bss) for frequency
    main.o(i.HAL_ADC_ConvCpltCallback) refers to main.o(.data) for .data
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue) for HAL_TIM_ReadCapturedValue
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to tim.o(.bss) for htim3
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to main.o(.bss) for .bss
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to main.o(.data) for .data
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to fsmc.o(i.MX_FSMC_Init) for MX_FSMC_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM8_Init) for MX_TIM8_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to adc.o(i.MX_ADC3_Init) for MX_ADC3_Init
    main.o(i.main) refers to lcd.o(i.lcd_init) for lcd_init
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) for HAL_TIM_IC_Start_IT
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(i.main) refers to arm_cfft_f32.o(.text) for arm_cfft_f32
    main.o(i.main) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    main.o(i.main) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(i.main) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.main) refers to round.o(i.__hardfp_round) for __hardfp_round
    main.o(i.main) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    main.o(i.main) refers to ad9833.o(i.AD9833_WaveSeting_No1) for AD9833_WaveSeting_No1
    main.o(i.main) refers to ad9833.o(i.AD9833_WaveSeting_No2) for AD9833_WaveSeting_No2
    main.o(i.main) refers to si5351a.o(i.si5351aSetFrequency) for si5351aSetFrequency
    main.o(i.main) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.main) refers to tim.o(.bss) for htim8
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to adc.o(.bss) for hadc1
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_f32_len1024
    main.o(i.main) refers to dpll_simple.o(i.DPLL_Simple_Init) for DPLL_Simple_Init
    main.o(i.main) refers to calculate.o(i.modeFilter) for modeFilter
    main.o(i.main) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.main) refers to dadd.o(.text) for __aeabi_dsub
    main.o(i.main) refers to dpll_simple.o(i.DPLL_Simple_Update) for DPLL_Simple_Update
    main.o(i.main) refers to key.o(i.phase_set) for phase_set
    main.o(i.main) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(i.main) refers to ad9833.o(i.AD9833_Write_No1) for AD9833_Write_No1
    main.o(i.main) refers to ad9833.o(i.AD9833_Write_No2) for AD9833_Write_No2
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to lcd.o(i.lcd_show_string) for lcd_show_string
    main.o(i.main) refers to dfixi.o(.text) for __aeabi_d2iz
    main.o(i.main) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.main) refers to dpll_simple.o(i.DPLL_Simple_IsLocked) for DPLL_Simple_IsLocked
    main.o(i.main) refers to dpll_simple.o(i.DPLL_Simple_GetPhaseError) for DPLL_Simple_GetPhaseError
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC3_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC3_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC3_Init) refers to adc.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    fsmc.o(i.HAL_FSMC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    fsmc.o(i.HAL_FSMC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fsmc.o(i.HAL_FSMC_MspInit) refers to fsmc.o(.data) for .data
    fsmc.o(i.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fsmc.o(i.HAL_SRAM_MspDeInit) refers to fsmc.o(.data) for .data
    fsmc.o(i.HAL_SRAM_MspInit) refers to fsmc.o(i.HAL_FSMC_MspInit) for HAL_FSMC_MspInit
    fsmc.o(i.MX_FSMC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    fsmc.o(i.MX_FSMC_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    fsmc.o(i.MX_FSMC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    fsmc.o(i.MX_FSMC_Init) refers to fsmc.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) for HAL_TIM_SlaveConfigSynchro
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM8_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM8_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fgetc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    usart.o(i.fgetc) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to adc.o(.bss) for hdma_adc3
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.TIM3_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for htim3
    dpll_app.o(i.DPLL_App_ApplyPhaseCorrection) refers to ad9833.o(i.AD9833_SetPhase_No1) for AD9833_SetPhase_No1
    dpll_app.o(i.DPLL_App_ApplyPhaseCorrection) refers to ad9833.o(i.AD9833_SetPhase_No2) for AD9833_SetPhase_No2
    dpll_app.o(i.DPLL_App_ApplyPhaseCorrection) refers to calculate.o(i.DPLL_GetTimerDelay) for DPLL_GetTimerDelay
    dpll_app.o(i.DPLL_App_ApplyPhaseCorrection) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_DeInit) refers to dpll_app.o(.data) for .data
    dpll_app.o(i.DPLL_App_GetStatus) refers to calculate.o(i.DPLL_RadToDeg) for DPLL_RadToDeg
    dpll_app.o(i.DPLL_App_GetStatus) refers to dpll_app.o(.data) for .data
    dpll_app.o(i.DPLL_App_GetStatus) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_Init) refers to d2f.o(.text) for __aeabi_d2f
    dpll_app.o(i.DPLL_App_Init) refers to calculate.o(i.DPLL_Init) for DPLL_Init
    dpll_app.o(i.DPLL_App_Init) refers to main.o(.bss) for frequency
    dpll_app.o(i.DPLL_App_Init) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_Init) refers to dpll_app.o(.data) for .data
    dpll_app.o(i.DPLL_App_IsLocked) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_Process) refers to d2f.o(.text) for __aeabi_d2f
    dpll_app.o(i.DPLL_App_Process) refers to calculate.o(i.DPLL_Update) for DPLL_Update
    dpll_app.o(i.DPLL_App_Process) refers to dpll_app.o(i.DPLL_App_ApplyPhaseCorrection) for DPLL_App_ApplyPhaseCorrection
    dpll_app.o(i.DPLL_App_Process) refers to dpll_app.o(.data) for .data
    dpll_app.o(i.DPLL_App_Process) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_Process) refers to main.o(.bss) for frequency
    dpll_app.o(i.DPLL_App_Reset) refers to calculate.o(i.DPLL_Init) for DPLL_Init
    dpll_app.o(i.DPLL_App_Reset) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_Reset) refers to dpll_app.o(.data) for .data
    dpll_app.o(i.DPLL_App_SetPhaseOffset) refers to calculate.o(i.DPLL_DegToRad) for DPLL_DegToRad
    dpll_app.o(i.DPLL_App_SetPhaseOffset) refers to dpll_app.o(i.DPLL_App_ApplyPhaseCorrection) for DPLL_App_ApplyPhaseCorrection
    dpll_app.o(i.DPLL_App_SetPhaseOffset) refers to dpll_app.o(.bss) for .bss
    dpll_app.o(i.DPLL_App_SetTargetFreq) refers to dpll_app.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_ApplyCorrection) refers to ad9833.o(i.AD9833_SetPhase_No1) for AD9833_SetPhase_No1
    dpll_simple.o(i.DPLL_Simple_ApplyCorrection) refers to ad9833.o(i.AD9833_SetPhase_No2) for AD9833_SetPhase_No2
    dpll_simple.o(i.DPLL_Simple_ApplyCorrection) refers to dpll_simple.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_Enable) refers to dpll_simple.o(.data) for .data
    dpll_simple.o(i.DPLL_Simple_GetPhaseError) refers to dpll_simple.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_Init) refers to dpll_simple.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_Init) refers to dpll_simple.o(.data) for .data
    dpll_simple.o(i.DPLL_Simple_IsLocked) refers to dpll_simple.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_PhaseDetect) refers to arm_sin_f32.o(.text) for arm_sin_f32
    dpll_simple.o(i.DPLL_Simple_Reset) refers to dpll_simple.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_SetPhaseOffset) refers to dpll_simple.o(i.DPLL_Simple_ApplyCorrection) for DPLL_Simple_ApplyCorrection
    dpll_simple.o(i.DPLL_Simple_SetPhaseOffset) refers to dpll_simple.o(.bss) for .bss
    dpll_simple.o(i.DPLL_Simple_Update) refers to dpll_simple.o(i.DPLL_Simple_PhaseDetect) for DPLL_Simple_PhaseDetect
    dpll_simple.o(i.DPLL_Simple_Update) refers to dpll_simple.o(i.DPLL_Simple_ApplyCorrection) for DPLL_Simple_ApplyCorrection
    dpll_simple.o(i.DPLL_Simple_Update) refers to dpll_simple.o(.data) for .data
    dpll_simple.o(i.DPLL_Simple_Update) refers to dpll_simple.o(.bss) for .bss
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to fsmc.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit) for FSMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to fsmc.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init) for FSMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable) for FSMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable) for FSMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to key.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to main.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to key.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    arm_sin_f32.o(.text) refers to arm_common_tables.o(.constdata) for sinTable_f32
    arm_cfft_f32.o(.text) refers to arm_cfft_radix8_f32.o(.text) for arm_radix8_butterfly_f32
    arm_cfft_f32.o(.text) refers to arm_bitreversal2.o(. text) for arm_bitreversal_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_16
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable16
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable4096
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_16_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_16
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_32_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_64_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_128_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_256_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_512_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_1024_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_2048_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096_q31
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_4096
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_16_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_16
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_32_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_64_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_128_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_256_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_512_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_1024_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_2048_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096_q15
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable_fixed_4096
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable32
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable64
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable128
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable256
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable512
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable1024
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable2048
    arm_const_structs.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_rfft_4096
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len16
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len32
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len64
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len128
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len256
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len512
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len1024
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len2048
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q31.o(.constdata) for realCoefAQ31
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len4096
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len16
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len32
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len64
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len128
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len256
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len512
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len1024
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len2048
    arm_const_structs.o(.constdata) refers to arm_rfft_init_q15.o(.constdata) for realCoefAQ15
    arm_const_structs.o(.constdata) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len4096
    arm_rfft_init_q15.o(.text) refers to arm_rfft_init_q15.o(.constdata) for .constdata
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len4096
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len2048
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len1024
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len512
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len256
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len128
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len64
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len32
    arm_rfft_init_q15.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q15_len16
    arm_rfft_init_q31.o(.text) refers to arm_rfft_init_q31.o(.constdata) for .constdata
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len4096
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len2048
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len1024
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len512
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len256
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len128
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len64
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len32
    arm_rfft_init_q31.o(.text) refers to arm_const_structs.o(.constdata) for arm_cfft_sR_q31_len16
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    round.o(i.__hardfp_round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(.text) for _drnd
    round.o(i.__hardfp_round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.__hardfp_round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.__hardfp_round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf_full.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_full.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_full.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_full.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_full.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_full.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_full_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_full_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_full_x.o(i.____hardfp_sqrtf$lsc) refers to d2f.o(.text) for __aeabi_d2f
    sqrtf_full_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_full_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_full_x.o(i.____softfp_sqrtf$lsc) refers to d2f.o(.text) for __aeabi_d2f
    sqrtf_full_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_full_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_full_x.o(i.__sqrtf$lsc) refers to d2f.o(.text) for __aeabi_d2f
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.lcd_color_fill), (88 bytes).
    Removing lcd.o(i.lcd_display_off), (32 bytes).
    Removing lcd.o(i.lcd_display_on), (32 bytes).
    Removing lcd.o(i.lcd_draw_circle), (196 bytes).
    Removing lcd.o(i.lcd_draw_hline), (40 bytes).
    Removing lcd.o(i.lcd_draw_line), (156 bytes).
    Removing lcd.o(i.lcd_draw_rectangle), (68 bytes).
    Removing lcd.o(i.lcd_fill), (72 bytes).
    Removing lcd.o(i.lcd_fill_circle), (182 bytes).
    Removing lcd.o(i.lcd_pow), (16 bytes).
    Removing lcd.o(i.lcd_read_point), (116 bytes).
    Removing lcd.o(i.lcd_set_window), (344 bytes).
    Removing lcd.o(i.lcd_show_num), (114 bytes).
    Removing lcd.o(i.lcd_show_xnum), (138 bytes).
    Removing lcd.o(.data), (4 bytes).
    Removing calculate.o(.rev16_text), (4 bytes).
    Removing calculate.o(.revsh_text), (4 bytes).
    Removing calculate.o(.rrx_text), (6 bytes).
    Removing calculate.o(i.DFT_PhaseCalculation), (56 bytes).
    Removing calculate.o(i.DFT_SingleFreq), (152 bytes).
    Removing calculate.o(i.DPLL_CalculatePhaseError), (64 bytes).
    Removing calculate.o(i.DPLL_DegToRad), (28 bytes).
    Removing calculate.o(i.DPLL_FrequencyCorrection), (76 bytes).
    Removing calculate.o(i.DPLL_GetTimerDelay), (40 bytes).
    Removing calculate.o(i.DPLL_Init), (36 bytes).
    Removing calculate.o(i.DPLL_IsLocked), (88 bytes).
    Removing calculate.o(i.DPLL_PhaseCorrection), (76 bytes).
    Removing calculate.o(i.DPLL_PhaseDetect), (112 bytes).
    Removing calculate.o(i.DPLL_RadToDeg), (28 bytes).
    Removing calculate.o(i.DPLL_Update), (276 bytes).
    Removing calculate.o(i.GenerateSinCosTable), (100 bytes).
    Removing calculate.o(i.medianFilter), (56 bytes).
    Removing calculate.o(.bss), (12000 bytes).
    Removing calculate.o(.data), (4 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing ad9833.o(.rrx_text), (6 bytes).
    Removing ad9833.o(i.AD9833_AmpSet_No1), (68 bytes).
    Removing ad9833.o(i.AD9833_AmpSet_No2), (68 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency_No1), (96 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency_No2), (96 bytes).
    Removing si5351a.o(.rev16_text), (4 bytes).
    Removing si5351a.o(.revsh_text), (4 bytes).
    Removing si5351a.o(.rrx_text), (6 bytes).
    Removing si5351a.o(i.Delay_ms), (26 bytes).
    Removing si5351a.o(i.I2C_RecvByte), (84 bytes).
    Removing si5351a.o(i.I2C_SendACK), (64 bytes).
    Removing si5351a.o(i.Single_ReadI2C), (80 bytes).
    Removing si5351a.o(i.Single_WriteI2C), (64 bytes).
    Removing relays.o(.rev16_text), (4 bytes).
    Removing relays.o(.revsh_text), (4 bytes).
    Removing relays.o(.rrx_text), (6 bytes).
    Removing relays.o(i.Relays_Close_Set), (16 bytes).
    Removing relays.o(i.Relays_Open_Set), (16 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (84 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing fsmc.o(.rev16_text), (4 bytes).
    Removing fsmc.o(.revsh_text), (4 bytes).
    Removing fsmc.o(.rrx_text), (6 bytes).
    Removing fsmc.o(i.HAL_SRAM_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (92 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (44 bytes).
    Removing usart.o(i.fgetc), (32 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing dpll_app.o(.rev16_text), (4 bytes).
    Removing dpll_app.o(.revsh_text), (4 bytes).
    Removing dpll_app.o(.rrx_text), (6 bytes).
    Removing dpll_app.o(i.DPLL_App_ApplyPhaseCorrection), (124 bytes).
    Removing dpll_app.o(i.DPLL_App_DeInit), (16 bytes).
    Removing dpll_app.o(i.DPLL_App_GetStatus), (124 bytes).
    Removing dpll_app.o(i.DPLL_App_Init), (76 bytes).
    Removing dpll_app.o(i.DPLL_App_IsLocked), (28 bytes).
    Removing dpll_app.o(i.DPLL_App_Process), (240 bytes).
    Removing dpll_app.o(i.DPLL_App_Reset), (60 bytes).
    Removing dpll_app.o(i.DPLL_App_SetPhaseOffset), (40 bytes).
    Removing dpll_app.o(i.DPLL_App_SetTargetFreq), (28 bytes).
    Removing dpll_app.o(.bss), (4056 bytes).
    Removing dpll_app.o(.data), (8 bytes).
    Removing dpll_simple.o(.rev16_text), (4 bytes).
    Removing dpll_simple.o(.revsh_text), (4 bytes).
    Removing dpll_simple.o(.rrx_text), (6 bytes).
    Removing dpll_simple.o(i.DPLL_Simple_Reset), (44 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (112 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (302 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (170 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (252 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (444 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (220 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (228 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (268 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (92 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (108 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (104 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (146 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (52 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_AttributeSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_CommonSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC), (80 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_Init), (72 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit), (50 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_AttributeSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_CommonSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_IOSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_Init), (40 bytes).
    Removing stm32f4xx_hal_nor.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_nor.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_nor.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (28 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (88 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (70 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (92 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (58 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (94 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (66 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (76 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACplt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMAError), (26 bytes).
    Removing stm32f4xx_hal_nand.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_nand.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_nand.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pccard.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pccard.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pccard.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (444 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (444 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (108 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (254 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (140 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (52 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler), (616 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (192 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (42 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (140 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (26 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndRxTransfer), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Receive_IT), (192 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (58 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing arm_sin_f32.o(.rev16_text), (4 bytes).
    Removing arm_sin_f32.o(.revsh_text), (4 bytes).
    Removing arm_sin_f32.o(.rrx_text), (6 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_f32.o(.rrx_text), (6 bytes).
    Removing arm_const_structs.o(.rev16_text), (4 bytes).
    Removing arm_const_structs.o(.revsh_text), (4 bytes).
    Removing arm_const_structs.o(.rrx_text), (6 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (16 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_const_structs.o(.constdata), (24 bytes).
    Removing arm_rfft_init_q15.o(.rev16_text), (4 bytes).
    Removing arm_rfft_init_q15.o(.revsh_text), (4 bytes).
    Removing arm_rfft_init_q15.o(.rrx_text), (6 bytes).
    Removing arm_rfft_init_q15.o(.text), (204 bytes).
    Removing arm_rfft_init_q15.o(.constdata), (32768 bytes).
    Removing arm_rfft_init_q31.o(.rev16_text), (4 bytes).
    Removing arm_rfft_init_q31.o(.revsh_text), (4 bytes).
    Removing arm_rfft_init_q31.o(.rrx_text), (6 bytes).
    Removing arm_rfft_init_q31.o(.text), (204 bytes).
    Removing arm_rfft_init_q31.o(.constdata), (65536 bytes).
    Removing arm_cfft_radix8_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix8_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix8_f32.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (32768 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (24 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (224 bytes).
    Removing arm_common_tables.o(.constdata), (480 bytes).
    Removing arm_common_tables.o(.constdata), (960 bytes).
    Removing arm_common_tables.o(.constdata), (1984 bytes).
    Removing arm_common_tables.o(.constdata), (3968 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (1026 bytes).

698 unused section(s) (total 356812 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/fsmc.c                       0x00000000   Number         0  fsmc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_nand.c 0x00000000   Number         0  stm32f4xx_hal_nand.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_nor.c 0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pccard.c 0x00000000   Number         0  stm32f4xx_hal_pccard.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_full_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_full.o ABSOLUTE
    ..\..\Source\CommonTables\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\..\Source\CommonTables\arm_const_structs.c 0x00000000   Number         0  arm_const_structs.o ABSOLUTE
    ..\..\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\..\Source\FastMathFunctions\arm_sin_f32.c 0x00000000   Number         0  arm_sin_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_f32.c 0x00000000   Number         0  arm_cfft_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_radix8_f32.c 0x00000000   Number         0  arm_cfft_radix8_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_rfft_init_q15.c 0x00000000   Number         0  arm_rfft_init_q15.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_rfft_init_q31.c 0x00000000   Number         0  arm_rfft_init_q31.o ABSOLUTE
    ..\Core\Src\AD9833.c                     0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\Core\Src\Relays.c                     0x00000000   Number         0  relays.o ABSOLUTE
    ..\Core\Src\SI5351A.c                    0x00000000   Number         0  si5351a.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\calculate.c                  0x00000000   Number         0  calculate.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\dpll_app.c                   0x00000000   Number         0  dpll_app.o ABSOLUTE
    ..\Core\Src\dpll_simple.c                0x00000000   Number         0  dpll_simple.o ABSOLUTE
    ..\Core\Src\fsmc.c                       0x00000000   Number         0  fsmc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\Core\Src\lcd.c                        0x00000000   Number         0  lcd.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_nand.c 0x00000000   Number         0  stm32f4xx_hal_nand.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_nor.c 0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pccard.c 0x00000000   Number         0  stm32f4xx_hal_pccard.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\..\\Source\\CommonTables\\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\..\\Source\\CommonTables\\arm_const_structs.c 0x00000000   Number         0  arm_const_structs.o ABSOLUTE
    ..\\..\\Source\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\..\\Source\\FastMathFunctions\\arm_sin_f32.c 0x00000000   Number         0  arm_sin_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_bitreversal2.S 0x00000000   Number         0  arm_bitreversal2.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_f32.c 0x00000000   Number         0  arm_cfft_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_radix8_f32.c 0x00000000   Number         0  arm_cfft_radix8_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_rfft_init_q15.c 0x00000000   Number         0  arm_rfft_init_q15.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_rfft_init_q31.c 0x00000000   Number         0  arm_rfft_init_q31.o ABSOLUTE
    ..\\Core\\Src\\AD9833.c                  0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\Core\\Src\\Relays.c                  0x00000000   Number         0  relays.o ABSOLUTE
    ..\\Core\\Src\\SI5351A.c                 0x00000000   Number         0  si5351a.o ABSOLUTE
    ..\\Core\\Src\\calculate.c               0x00000000   Number         0  calculate.o ABSOLUTE
    ..\\Core\\Src\\dpll_app.c                0x00000000   Number         0  dpll_app.o ABSOLUTE
    ..\\Core\\Src\\dpll_simple.c             0x00000000   Number         0  dpll_simple.o ABSOLUTE
    ..\\Core\\Src\\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\\Core\\Src\\lcd.c                     0x00000000   Number         0  lcd.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    . text                                   0x08000188   Section      192  arm_bitreversal2.o(. text)
    $v0                                      0x08000188   Number         0  arm_bitreversal2.o(. text)
    .ARM.Collect$$$$00000000                 0x08000248   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000248   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800024c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000250   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000250   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000250   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000258   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800025c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800025c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800025c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800025c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000260   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x08000260   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000284   Section        0  arm_sin_f32.o(.text)
    .text                                    0x08000318   Section        0  arm_cmplx_mag_f32.o(.text)
    .text                                    0x08000414   Section        0  arm_cfft_f32.o(.text)
    .text                                    0x08000b38   Section        0  arm_cfft_radix8_f32.o(.text)
    .text                                    0x08001014   Section        0  uldiv.o(.text)
    .text                                    0x08001076   Section        0  memseta.o(.text)
    .text                                    0x0800109a   Section        0  dadd.o(.text)
    .text                                    0x080011e8   Section        0  dmul.o(.text)
    .text                                    0x080012cc   Section        0  ddiv.o(.text)
    .text                                    0x080013aa   Section        0  dfltui.o(.text)
    .text                                    0x080013c4   Section        0  dfixi.o(.text)
    .text                                    0x08001402   Section        0  dfixui.o(.text)
    .text                                    0x08001434   Section        0  f2d.o(.text)
    .text                                    0x0800145c   Section       48  cdcmple.o(.text)
    .text                                    0x0800148c   Section        0  d2f.o(.text)
    .text                                    0x080014c4   Section        0  uidiv.o(.text)
    .text                                    0x080014f0   Section        0  llshl.o(.text)
    .text                                    0x0800150e   Section        0  llushr.o(.text)
    .text                                    0x0800152e   Section        0  llsshr.o(.text)
    .text                                    0x08001552   Section        0  iusefp.o(.text)
    .text                                    0x08001552   Section        0  fepilogue.o(.text)
    .text                                    0x080015c0   Section        0  depilogue.o(.text)
    .text                                    0x0800167c   Section        0  drnd.o(.text)
    .text                                    0x08001704   Section        0  dfixul.o(.text)
    .text                                    0x08001734   Section       48  cdrcmple.o(.text)
    .text                                    0x08001764   Section       36  init.o(.text)
    i.AD9833_Delay                           0x08001788   Section        0  ad9833.o(i.AD9833_Delay)
    AD9833_Delay                             0x08001789   Thumb Code    12  ad9833.o(i.AD9833_Delay)
    i.AD9833_SetPhase_No1                    0x08001794   Section        0  ad9833.o(i.AD9833_SetPhase_No1)
    i.AD9833_SetPhase_No2                    0x0800179a   Section        0  ad9833.o(i.AD9833_SetPhase_No2)
    i.AD9833_WaveSeting_No1                  0x080017a0   Section        0  ad9833.o(i.AD9833_WaveSeting_No1)
    i.AD9833_WaveSeting_No2                  0x08001850   Section        0  ad9833.o(i.AD9833_WaveSeting_No2)
    i.AD9833_Write_No1                       0x08001900   Section        0  ad9833.o(i.AD9833_Write_No1)
    i.AD9833_Write_No2                       0x08001970   Section        0  ad9833.o(i.AD9833_Write_No2)
    i.ADC_DMAConvCplt                        0x080019e0   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x080019e1   Thumb Code   110  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08001a4e   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x08001a4f   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08001a64   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x08001a65   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Init                               0x08001a70   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08001a71   Thumb Code   284  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x08001b98   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream0_IRQHandler                0x08001b9c   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x08001ba8   Section        0  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08001bb4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08001bb5   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08001bdc   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08001bdd   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08001c30   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001c31   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DPLL_Simple_ApplyCorrection            0x08001c58   Section        0  dpll_simple.o(i.DPLL_Simple_ApplyCorrection)
    i.DPLL_Simple_Enable                     0x08001cb4   Section        0  dpll_simple.o(i.DPLL_Simple_Enable)
    i.DPLL_Simple_GetPhaseError              0x08001cc0   Section        0  dpll_simple.o(i.DPLL_Simple_GetPhaseError)
    i.DPLL_Simple_Init                       0x08001cec   Section        0  dpll_simple.o(i.DPLL_Simple_Init)
    i.DPLL_Simple_IsLocked                   0x08001d2c   Section        0  dpll_simple.o(i.DPLL_Simple_IsLocked)
    i.DPLL_Simple_PhaseDetect                0x08001d48   Section        0  dpll_simple.o(i.DPLL_Simple_PhaseDetect)
    DPLL_Simple_PhaseDetect                  0x08001d49   Thumb Code   110  dpll_simple.o(i.DPLL_Simple_PhaseDetect)
    i.DPLL_Simple_SetPhaseOffset             0x08001dd0   Section        0  dpll_simple.o(i.DPLL_Simple_SetPhaseOffset)
    i.DPLL_Simple_Update                     0x08001e00   Section        0  dpll_simple.o(i.DPLL_Simple_Update)
    i.DebugMon_Handler                       0x08001f30   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_1us                              0x08001f32   Section        0  si5351a.o(i.Delay_1us)
    i.Error_Handler                          0x08001f4a   Section        0  main.o(i.Error_Handler)
    i.FSMC_NORSRAM_Extended_Timing_Init      0x08001f50   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    i.FSMC_NORSRAM_Init                      0x08001f8c   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    i.FSMC_NORSRAM_Timing_Init               0x08001ff4   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    i.HAL_ADC_ConfigChannel                  0x08002038   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002184   Section        0  main.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x0800219c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x0800219e   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x080021a0   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x080021f4   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x080022f4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_IRQHandler                     0x08002448   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080025e8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080026bc   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x0800272c   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_FSMC_MspInit                       0x08002750   Section        0  fsmc.o(i.HAL_FSMC_MspInit)
    HAL_FSMC_MspInit                         0x08002751   Thumb Code   144  fsmc.o(i.HAL_FSMC_MspInit)
    i.HAL_GPIO_Init                          0x080027f8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080029e8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080029f2   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080029fc   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002a08   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002a18   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002a4c   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002a8c   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002abc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002ad8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002b18   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002b3c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08002c70   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002c90   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002cb0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002d10   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x0800307c   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x080030d8   Section        0  fsmc.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_Config                     0x080030dc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08003104   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08003106   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003108   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003198   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080031f4   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x080032b0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x08003328   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Base_Stop                      0x080033a8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    i.HAL_TIM_ConfigClockSource              0x080033ce   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_IC_CaptureCallback             0x080034ac   Section        0  main.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IC_ConfigChannel               0x080034e4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    i.HAL_TIM_IC_Init                        0x08003602   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    i.HAL_TIM_IC_MspInit                     0x0800365c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    i.HAL_TIM_IC_Start_IT                    0x08003660   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    i.HAL_TIM_IRQHandler                     0x0800376c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080038d2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x080038d4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x080038d8   Section        0  key.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_ReadCapturedValue              0x0800399c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    i.HAL_TIM_SlaveConfigSynchro             0x080039c6   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro)
    i.HAL_TIM_TriggerCallback                0x08003a1c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UART_Init                          0x08003a1e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003a80   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x08003aec   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HardFault_Handler                      0x08003b9e   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_SendByte                           0x08003ba0   Section        0  si5351a.o(i.I2C_SendByte)
    i.I2C_Start                              0x08003bf4   Section        0  si5351a.o(i.I2C_Start)
    i.I2C_Stop                               0x08003c60   Section        0  si5351a.o(i.I2C_Stop)
    i.I2C_WaitAck                            0x08003ca0   Section        0  si5351a.o(i.I2C_WaitAck)
    i.MX_ADC1_Init                           0x08003d14   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_ADC3_Init                           0x08003d78   Section        0  adc.o(i.MX_ADC3_Init)
    i.MX_DMA_Init                            0x08003ddc   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FSMC_Init                           0x08003e18   Section        0  fsmc.o(i.MX_FSMC_Init)
    i.MX_GPIO_Init                           0x08003ea0   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM2_Init                           0x08003fb0   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08004018   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM8_Init                           0x080040d0   Section        0  tim.o(i.MX_TIM8_Init)
    i.MX_USART1_UART_Init                    0x08004138   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08004170   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004172   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08004174   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08004176   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08004178   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x0800417c   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08004214   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08004224   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x08004230   Section        0  stm32f4xx_it.o(i.TIM3_IRQHandler)
    i.TIM_Base_SetConfig                     0x0800423c   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08004304   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x0800431e   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08004332   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08004333   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_SlaveTimer_SetConfig               0x08004342   Section        0  stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig)
    TIM_SlaveTimer_SetConfig                 0x08004343   Thumb Code   140  stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x080043ce   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x080043cf   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI1_SetConfig                      0x080043f0   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    i.TIM_TI2_ConfigInputStage               0x08004470   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08004471   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.TIM_TI2_SetConfig                      0x08004494   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    TIM_TI2_SetConfig                        0x08004495   Thumb Code    54  stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    i.UART_SetConfig                         0x080044cc   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080044cd   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x080045d8   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080045d9   Thumb Code   120  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.UsageFault_Handler                     0x08004650   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08004654   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08004674   Section        0  printfa.o(i.__0sprintf)
    i.__NVIC_SetPriority                     0x0800469c   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x0800469d   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_round                         0x080046c0   Section        0  round.o(i.__hardfp_round)
    i.__scatterload_copy                     0x080047a0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080047ae   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080047b0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080047c0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080047c1   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08004944   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08004945   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08004ff8   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08004ff9   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x0800501c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x0800501d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x0800504a   Section        0  printfa.o(i._sputc)
    _sputc                                   0x0800504b   Thumb Code    10  printfa.o(i._sputc)
    i.fputc                                  0x08005054   Section        0  usart.o(i.fputc)
    i.free                                   0x0800506c   Section        0  malloc.o(i.free)
    i.i2cSendRegister                        0x080050bc   Section        0  si5351a.o(i.i2cSendRegister)
    i.lcd_clear                              0x080050f0   Section        0  lcd.o(i.lcd_clear)
    i.lcd_display_dir                        0x08005120   Section        0  lcd.o(i.lcd_display_dir)
    i.lcd_draw_point                         0x0800520c   Section        0  lcd.o(i.lcd_draw_point)
    i.lcd_ex_ili9341_reginit                 0x08005222   Section        0  lcd.o(i.lcd_ex_ili9341_reginit)
    i.lcd_ex_ili9806_reginit                 0x08005450   Section        0  lcd.o(i.lcd_ex_ili9806_reginit)
    i.lcd_ex_nt35310_reginit                 0x08005792   Section        0  lcd.o(i.lcd_ex_nt35310_reginit)
    i.lcd_ex_nt35510_reginit                 0x08006686   Section        0  lcd.o(i.lcd_ex_nt35510_reginit)
    i.lcd_ex_ssd1963_reginit                 0x08007580   Section        0  lcd.o(i.lcd_ex_ssd1963_reginit)
    i.lcd_ex_st7789_reginit                  0x080076f0   Section        0  lcd.o(i.lcd_ex_st7789_reginit)
    i.lcd_ex_st7796_reginit                  0x0800789a   Section        0  lcd.o(i.lcd_ex_st7796_reginit)
    i.lcd_init                               0x08007a64   Section        0  lcd.o(i.lcd_init)
    i.lcd_rd_data                            0x08007ce0   Section        0  lcd.o(i.lcd_rd_data)
    lcd_rd_data                              0x08007ce1   Thumb Code    26  lcd.o(i.lcd_rd_data)
    i.lcd_scan_dir                           0x08007cfc   Section        0  lcd.o(i.lcd_scan_dir)
    i.lcd_set_cursor                         0x08007e80   Section        0  lcd.o(i.lcd_set_cursor)
    i.lcd_show_char                          0x08007f74   Section        0  lcd.o(i.lcd_show_char)
    i.lcd_show_string                        0x08008050   Section        0  lcd.o(i.lcd_show_string)
    i.lcd_ssd_backlight_set                  0x080080a8   Section        0  lcd.o(i.lcd_ssd_backlight_set)
    i.lcd_wr_data                            0x080080f8   Section        0  lcd.o(i.lcd_wr_data)
    i.lcd_wr_regno                           0x08008110   Section        0  lcd.o(i.lcd_wr_regno)
    i.lcd_write_ram_prepare                  0x08008128   Section        0  lcd.o(i.lcd_write_ram_prepare)
    i.lcd_write_reg                          0x0800813c   Section        0  lcd.o(i.lcd_write_reg)
    i.main                                   0x08008148   Section        0  main.o(i.main)
    i.malloc                                 0x080089e8   Section        0  malloc.o(i.malloc)
    i.modeFilter                             0x08008a54   Section        0  calculate.o(i.modeFilter)
    i.phase_set                              0x08008ad0   Section        0  key.o(i.phase_set)
    i.setupMultisynth                        0x08008bd0   Section        0  si5351a.o(i.setupMultisynth)
    i.setupPLL                               0x08008c3c   Section        0  si5351a.o(i.setupPLL)
    i.si5351aSetFrequency                    0x08008d00   Section        0  si5351a.o(i.si5351aSetFrequency)
    .constdata                               0x08008dbc   Section    12160  lcd.o(.constdata)
    .constdata                               0x0800bd3c   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800bd3c   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800bd44   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800bd54   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800bd5c   Section       16  arm_const_structs.o(.constdata)
    .constdata                               0x0800bd6c   Section     8192  arm_common_tables.o(.constdata)
    .constdata                               0x0800dd6c   Section     3600  arm_common_tables.o(.constdata)
    .constdata                               0x0800eb7c   Section     2052  arm_common_tables.o(.constdata)
    .data                                    0x20000000   Section        4  lcd.o(.data)
    .data                                    0x20000004   Section       12  key.o(.data)
    dpll_phase_offset                        0x20000008   Data           4  key.o(.data)
    dpll_phase_offset                        0x2000000c   Data           4  key.o(.data)
    .data                                    0x20000010   Section      104  main.o(.data)
    .data                                    0x20000078   Section        8  fsmc.o(.data)
    FSMC_Initialized                         0x20000078   Data           4  fsmc.o(.data)
    FSMC_DeInitialized                       0x2000007c   Data           4  fsmc.o(.data)
    .data                                    0x20000080   Section        1  dpll_simple.o(.data)
    dpll_simple_enabled                      0x20000080   Data           1  dpll_simple.o(.data)
    .data                                    0x20000084   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000090   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000094   Section        4  stdout.o(.data)
    .data                                    0x20000098   Section        4  mvars.o(.data)
    .data                                    0x2000009c   Section        4  mvars.o(.data)
    .bss                                     0x200000a0   Section       94  lcd.o(.bss)
    .bss                                     0x200000fe   Section       15  key.o(.bss)
    .bss                                     0x20000110   Section    14392  main.o(.bss)
    .bss                                     0x20003948   Section      336  adc.o(.bss)
    .bss                                     0x20003a98   Section       80  fsmc.o(.bss)
    .bss                                     0x20003ae8   Section      216  tim.o(.bss)
    .bss                                     0x20003bc0   Section       68  usart.o(.bss)
    .bss                                     0x20003c04   Section       40  dpll_simple.o(.bss)
    dpll_ch1                                 0x20003c04   Data          20  dpll_simple.o(.bss)
    dpll_ch2                                 0x20003c18   Data          20  dpll_simple.o(.bss)
    HEAP                                     0x20003c30   Section      512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20003e30   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    arm_bitreversal_32                       0x08000189   Thumb Code   106  arm_bitreversal2.o(. text)
    arm_bitreversal_16                       0x080001f3   Thumb Code    86  arm_bitreversal2.o(. text)
    __main                                   0x08000249   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000249   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800024d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000251   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000251   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000251   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000251   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000259   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800025d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800025d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000261   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f407xx.o(.text)
    arm_sin_f32                              0x08000285   Thumb Code   130  arm_sin_f32.o(.text)
    arm_cmplx_mag_f32                        0x08000319   Thumb Code   246  arm_cmplx_mag_f32.o(.text)
    arm_cfft_radix8by2_f32                   0x08000415   Thumb Code   398  arm_cfft_f32.o(.text)
    arm_cfft_radix8by4_f32                   0x080005a3   Thumb Code  1098  arm_cfft_f32.o(.text)
    arm_cfft_f32                             0x080009ed   Thumb Code   330  arm_cfft_f32.o(.text)
    arm_radix8_butterfly_f32                 0x08000b39   Thumb Code  1244  arm_cfft_radix8_f32.o(.text)
    __aeabi_uldivmod                         0x08001015   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08001077   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08001077   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08001077   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08001085   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08001085   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08001085   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08001089   Thumb Code    18  memseta.o(.text)
    __aeabi_dadd                             0x0800109b   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080011dd   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080011e3   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080011e9   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080012cd   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x080013ab   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2iz                             0x080013c5   Thumb Code    62  dfixi.o(.text)
    __aeabi_d2uiz                            0x08001403   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x08001435   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x0800145d   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x0800145d   Thumb Code    48  cdcmple.o(.text)
    __aeabi_d2f                              0x0800148d   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080014c5   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080014c5   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080014f1   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080014f1   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800150f   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800150f   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800152f   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800152f   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08001553   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08001553   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08001565   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x080015c1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080015df   Thumb Code   156  depilogue.o(.text)
    _drnd                                    0x0800167d   Thumb Code   132  drnd.o(.text)
    __aeabi_d2ulz                            0x08001705   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08001735   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08001765   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08001765   Thumb Code     0  init.o(.text)
    AD9833_SetPhase_No1                      0x08001795   Thumb Code     6  ad9833.o(i.AD9833_SetPhase_No1)
    AD9833_SetPhase_No2                      0x0800179b   Thumb Code     6  ad9833.o(i.AD9833_SetPhase_No2)
    AD9833_WaveSeting_No1                    0x080017a1   Thumb Code   160  ad9833.o(i.AD9833_WaveSeting_No1)
    AD9833_WaveSeting_No2                    0x08001851   Thumb Code   160  ad9833.o(i.AD9833_WaveSeting_No2)
    AD9833_Write_No1                         0x08001901   Thumb Code   108  ad9833.o(i.AD9833_Write_No1)
    AD9833_Write_No2                         0x08001971   Thumb Code   108  ad9833.o(i.AD9833_Write_No2)
    BusFault_Handler                         0x08001b99   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream0_IRQHandler                  0x08001b9d   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08001ba9   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    DPLL_Simple_ApplyCorrection              0x08001c59   Thumb Code    78  dpll_simple.o(i.DPLL_Simple_ApplyCorrection)
    DPLL_Simple_Enable                       0x08001cb5   Thumb Code     6  dpll_simple.o(i.DPLL_Simple_Enable)
    DPLL_Simple_GetPhaseError                0x08001cc1   Thumb Code    32  dpll_simple.o(i.DPLL_Simple_GetPhaseError)
    DPLL_Simple_Init                         0x08001ced   Thumb Code    50  dpll_simple.o(i.DPLL_Simple_Init)
    DPLL_Simple_IsLocked                     0x08001d2d   Thumb Code    22  dpll_simple.o(i.DPLL_Simple_IsLocked)
    DPLL_Simple_SetPhaseOffset               0x08001dd1   Thumb Code    34  dpll_simple.o(i.DPLL_Simple_SetPhaseOffset)
    DPLL_Simple_Update                       0x08001e01   Thumb Code   278  dpll_simple.o(i.DPLL_Simple_Update)
    DebugMon_Handler                         0x08001f31   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_1us                                0x08001f33   Thumb Code    24  si5351a.o(i.Delay_1us)
    Error_Handler                            0x08001f4b   Thumb Code     4  main.o(i.Error_Handler)
    FSMC_NORSRAM_Extended_Timing_Init        0x08001f51   Thumb Code    56  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    FSMC_NORSRAM_Init                        0x08001f8d   Thumb Code    98  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    FSMC_NORSRAM_Timing_Init                 0x08001ff5   Thumb Code    64  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    HAL_ADC_ConfigChannel                    0x08002039   Thumb Code   316  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002185   Thumb Code    16  main.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x0800219d   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x0800219f   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x080021a1   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x080021f5   Thumb Code   228  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x080022f5   Thumb Code   302  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_IRQHandler                       0x08002449   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080025e9   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080026bd   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x0800272d   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080027f9   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080029e9   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080029f3   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080029fd   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002a09   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002a19   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002a4d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002a8d   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002abd   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002ad9   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002b19   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002b3d   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002c71   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002c91   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002cb1   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002d11   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x0800307d   Thumb Code    92  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x080030d9   Thumb Code     4  fsmc.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x080030dd   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08003105   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003107   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08003109   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003199   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080031f5   Thumb Code   170  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x080032b1   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x08003329   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop                        0x080033a9   Thumb Code    38  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    HAL_TIM_ConfigClockSource                0x080033cf   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x080034ad   Thumb Code    44  main.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_ConfigChannel                 0x080034e5   Thumb Code   286  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08003603   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x0800365d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_IT                      0x08003661   Thumb Code   240  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    HAL_TIM_IRQHandler                       0x0800376d   Thumb Code   358  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x080038d3   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x080038d5   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x080038d9   Thumb Code   184  key.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_ReadCapturedValue                0x0800399d   Thumb Code    42  stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    HAL_TIM_SlaveConfigSynchro               0x080039c7   Thumb Code    86  stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro)
    HAL_TIM_TriggerCallback                  0x08003a1d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UART_Init                            0x08003a1f   Thumb Code    98  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003a81   Thumb Code    94  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08003aed   Thumb Code   178  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HardFault_Handler                        0x08003b9f   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    I2C_SendByte                             0x08003ba1   Thumb Code    80  si5351a.o(i.I2C_SendByte)
    I2C_Start                                0x08003bf5   Thumb Code   102  si5351a.o(i.I2C_Start)
    I2C_Stop                                 0x08003c61   Thumb Code    60  si5351a.o(i.I2C_Stop)
    I2C_WaitAck                              0x08003ca1   Thumb Code   110  si5351a.o(i.I2C_WaitAck)
    MX_ADC1_Init                             0x08003d15   Thumb Code    92  adc.o(i.MX_ADC1_Init)
    MX_ADC3_Init                             0x08003d79   Thumb Code    92  adc.o(i.MX_ADC3_Init)
    MX_DMA_Init                              0x08003ddd   Thumb Code    56  dma.o(i.MX_DMA_Init)
    MX_FSMC_Init                             0x08003e19   Thumb Code   128  fsmc.o(i.MX_FSMC_Init)
    MX_GPIO_Init                             0x08003ea1   Thumb Code   254  gpio.o(i.MX_GPIO_Init)
    MX_TIM2_Init                             0x08003fb1   Thumb Code    98  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08004019   Thumb Code   176  tim.o(i.MX_TIM3_Init)
    MX_TIM8_Init                             0x080040d1   Thumb Code    96  tim.o(i.MX_TIM8_Init)
    MX_USART1_UART_Init                      0x08004139   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08004171   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004173   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08004175   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08004177   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08004179   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x0800417d   Thumb Code   142  main.o(i.SystemClock_Config)
    SystemInit                               0x08004215   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08004225   Thumb Code     6  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x08004231   Thumb Code     6  stm32f4xx_it.o(i.TIM3_IRQHandler)
    TIM_Base_SetConfig                       0x0800423d   Thumb Code   156  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08004305   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x0800431f   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_TI1_SetConfig                        0x080043f1   Thumb Code   100  stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    UsageFault_Handler                       0x08004651   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x08004655   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08004655   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08004655   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08004655   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08004655   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08004675   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08004675   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08004675   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08004675   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08004675   Thumb Code     0  printfa.o(i.__0sprintf)
    __hardfp_round                           0x080046c1   Thumb Code   194  round.o(i.__hardfp_round)
    __scatterload_copy                       0x080047a1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080047af   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080047b1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x08005055   Thumb Code    20  usart.o(i.fputc)
    free                                     0x0800506d   Thumb Code    76  malloc.o(i.free)
    i2cSendRegister                          0x080050bd   Thumb Code    52  si5351a.o(i.i2cSendRegister)
    lcd_clear                                0x080050f1   Thumb Code    44  lcd.o(i.lcd_clear)
    lcd_display_dir                          0x08005121   Thumb Code   230  lcd.o(i.lcd_display_dir)
    lcd_draw_point                           0x0800520d   Thumb Code    22  lcd.o(i.lcd_draw_point)
    lcd_ex_ili9341_reginit                   0x08005223   Thumb Code   558  lcd.o(i.lcd_ex_ili9341_reginit)
    lcd_ex_ili9806_reginit                   0x08005451   Thumb Code   834  lcd.o(i.lcd_ex_ili9806_reginit)
    lcd_ex_nt35310_reginit                   0x08005793   Thumb Code  3828  lcd.o(i.lcd_ex_nt35310_reginit)
    lcd_ex_nt35510_reginit                   0x08006687   Thumb Code  3834  lcd.o(i.lcd_ex_nt35510_reginit)
    lcd_ex_ssd1963_reginit                   0x08007581   Thumb Code   368  lcd.o(i.lcd_ex_ssd1963_reginit)
    lcd_ex_st7789_reginit                    0x080076f1   Thumb Code   426  lcd.o(i.lcd_ex_st7789_reginit)
    lcd_ex_st7796_reginit                    0x0800789b   Thumb Code   456  lcd.o(i.lcd_ex_st7796_reginit)
    lcd_init                                 0x08007a65   Thumb Code   616  lcd.o(i.lcd_init)
    lcd_scan_dir                             0x08007cfd   Thumb Code   384  lcd.o(i.lcd_scan_dir)
    lcd_set_cursor                           0x08007e81   Thumb Code   238  lcd.o(i.lcd_set_cursor)
    lcd_show_char                            0x08007f75   Thumb Code   196  lcd.o(i.lcd_show_char)
    lcd_show_string                          0x08008051   Thumb Code    86  lcd.o(i.lcd_show_string)
    lcd_ssd_backlight_set                    0x080080a9   Thumb Code    72  lcd.o(i.lcd_ssd_backlight_set)
    lcd_wr_data                              0x080080f9   Thumb Code    24  lcd.o(i.lcd_wr_data)
    lcd_wr_regno                             0x08008111   Thumb Code    24  lcd.o(i.lcd_wr_regno)
    lcd_write_ram_prepare                    0x08008129   Thumb Code    14  lcd.o(i.lcd_write_ram_prepare)
    lcd_write_reg                            0x0800813d   Thumb Code    12  lcd.o(i.lcd_write_reg)
    main                                     0x08008149   Thumb Code  1966  main.o(i.main)
    malloc                                   0x080089e9   Thumb Code    92  malloc.o(i.malloc)
    modeFilter                               0x08008a55   Thumb Code   124  calculate.o(i.modeFilter)
    phase_set                                0x08008ad1   Thumb Code   226  key.o(i.phase_set)
    setupMultisynth                          0x08008bd1   Thumb Code   104  si5351a.o(i.setupMultisynth)
    setupPLL                                 0x08008c3d   Thumb Code   190  si5351a.o(i.setupPLL)
    si5351aSetFrequency                      0x08008d01   Thumb Code   168  si5351a.o(i.si5351aSetFrequency)
    asc2_1206                                0x08008dbc   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x08009230   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x08009820   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x0800a57c   Data        6080  lcd.o(.constdata)
    AHBPrescTable                            0x0800bd44   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800bd54   Data           8  system_stm32f4xx.o(.constdata)
    arm_cfft_sR_f32_len1024                  0x0800bd5c   Data          16  arm_const_structs.o(.constdata)
    twiddleCoef_1024                         0x0800bd6c   Data        8192  arm_common_tables.o(.constdata)
    armBitRevIndexTable1024                  0x0800dd6c   Data        3600  arm_common_tables.o(.constdata)
    sinTable_f32                             0x0800eb7c   Data        2052  arm_common_tables.o(.constdata)
    Region$$Table$$Base                      0x0800f380   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800f3a0   Number         0  anon$$obj.o(Region$$Table)
    g_back_color                             0x20000000   Data           4  lcd.o(.data)
    is_phase_compensation_done               0x20000004   Data           1  key.o(.data)
    tim2_cnt                                 0x20000006   Data           2  key.o(.data)
    do_ad_flagc                              0x20000010   Data           1  main.o(.data)
    is_C_judge_done                          0x20000011   Data           1  main.o(.data)
    mean_filter_cnt                          0x20000012   Data           1  main.o(.data)
    is_capture_A_done                        0x20000013   Data           1  main.o(.data)
    dpll_mode_enabled                        0x20000014   Data           1  main.o(.data)
    max_index                                0x20000016   Data           2  main.o(.data)
    second_max_index                         0x20000018   Data           2  main.o(.data)
    wave_type                                0x2000001a   Data           2  main.o(.data)
    m_phase                                  0x2000001c   Data           2  main.o(.data)
    capture_A_index                          0x20000020   Data           4  main.o(.data)
    mag_1harmo                               0x20000024   Data           8  main.o(.data)
    mag_3harmo                               0x2000002c   Data           8  main.o(.data)
    mag_5harmo                               0x20000034   Data           8  main.o(.data)
    sum_mag_1harmo                           0x2000003c   Data           8  main.o(.data)
    sum_mag_3harmo                           0x20000044   Data           8  main.o(.data)
    sum_mag_5harmo                           0x2000004c   Data           8  main.o(.data)
    ave_mag_1harmo                           0x20000054   Data           8  main.o(.data)
    ave_mag_3harmo                           0x2000005c   Data           8  main.o(.data)
    ave_mag_5harmo                           0x20000064   Data           8  main.o(.data)
    frequency_bias                           0x20000070   Data           8  main.o(.data)
    uwTickFreq                               0x20000084   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000088   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x2000008c   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000090   Data           4  system_stm32f4xx.o(.data)
    __stdout                                 0x20000094   Data           4  stdout.o(.data)
    __microlib_freelist                      0x20000098   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x2000009c   Data           4  mvars.o(.data)
    g_sram_handle                            0x200000a0   Data          80  lcd.o(.bss)
    lcddev                                   0x200000f0   Data          14  lcd.o(.bss)
    key                                      0x200000fe   Data          15  key.o(.bss)
    FFT_Input                                0x20000110   Data        8192  main.o(.bss)
    FFT_Output                               0x20002110   Data        4096  main.o(.bss)
    adc_buf_C                                0x20003110   Data        2048  main.o(.bss)
    frequency                                0x20003910   Data          16  main.o(.bss)
    capture_cnt_A                            0x20003920   Data          20  main.o(.bss)
    Lcd_String                               0x20003934   Data          20  main.o(.bss)
    hadc1                                    0x20003948   Data          72  adc.o(.bss)
    hadc3                                    0x20003990   Data          72  adc.o(.bss)
    hdma_adc1                                0x200039d8   Data          96  adc.o(.bss)
    hdma_adc3                                0x20003a38   Data          96  adc.o(.bss)
    hsram1                                   0x20003a98   Data          80  fsmc.o(.bss)
    htim2                                    0x20003ae8   Data          72  tim.o(.bss)
    htim3                                    0x20003b30   Data          72  tim.o(.bss)
    htim8                                    0x20003b78   Data          72  tim.o(.bss)
    huart1                                   0x20003bc0   Data          68  usart.o(.bss)
    __heap_base                              0x20003c30   Data           0  startup_stm32f407xx.o(HEAP)
    __heap_limit                             0x20003e30   Data           0  startup_stm32f407xx.o(HEAP)
    __initial_sp                             0x20004230   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000249

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000f440, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000f3a0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x000000c0   Code   RO         4754    . text              arm_cortexM4lf_math.lib(arm_bitreversal2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         4892  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000248   0x08000248   0x00000004   Code   RO         5225    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800024c   0x0800024c   0x00000004   Code   RO         5228    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000250   0x08000250   0x00000000   Code   RO         5230    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000250   0x08000250   0x00000000   Code   RO         5232    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000250   0x08000250   0x00000008   Code   RO         5233    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000258   0x08000258   0x00000004   Code   RO         5240    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800025c   0x0800025c   0x00000000   Code   RO         5235    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800025c   0x0800025c   0x00000000   Code   RO         5237    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800025c   0x0800025c   0x00000004   Code   RO         5226    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000260   0x08000260   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000284   0x08000284   0x00000094   Code   RO         4497    .text               arm_cortexM4lf_math.lib(arm_sin_f32.o)
    0x08000318   0x08000318   0x000000fc   Code   RO         4547    .text               arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08000414   0x08000414   0x00000722   Code   RO         4574    .text               arm_cortexM4lf_math.lib(arm_cfft_f32.o)
    0x08000b36   0x08000b36   0x00000002   PAD
    0x08000b38   0x08000b38   0x000004dc   Code   RO         4733    .text               arm_cortexM4lf_math.lib(arm_cfft_radix8_f32.o)
    0x08001014   0x08001014   0x00000062   Code   RO         4895    .text               mc_w.l(uldiv.o)
    0x08001076   0x08001076   0x00000024   Code   RO         4897    .text               mc_w.l(memseta.o)
    0x0800109a   0x0800109a   0x0000014e   Code   RO         5188    .text               mf_w.l(dadd.o)
    0x080011e8   0x080011e8   0x000000e4   Code   RO         5190    .text               mf_w.l(dmul.o)
    0x080012cc   0x080012cc   0x000000de   Code   RO         5192    .text               mf_w.l(ddiv.o)
    0x080013aa   0x080013aa   0x0000001a   Code   RO         5194    .text               mf_w.l(dfltui.o)
    0x080013c4   0x080013c4   0x0000003e   Code   RO         5196    .text               mf_w.l(dfixi.o)
    0x08001402   0x08001402   0x00000032   Code   RO         5198    .text               mf_w.l(dfixui.o)
    0x08001434   0x08001434   0x00000026   Code   RO         5200    .text               mf_w.l(f2d.o)
    0x0800145a   0x0800145a   0x00000002   PAD
    0x0800145c   0x0800145c   0x00000030   Code   RO         5202    .text               mf_w.l(cdcmple.o)
    0x0800148c   0x0800148c   0x00000038   Code   RO         5204    .text               mf_w.l(d2f.o)
    0x080014c4   0x080014c4   0x0000002c   Code   RO         5242    .text               mc_w.l(uidiv.o)
    0x080014f0   0x080014f0   0x0000001e   Code   RO         5244    .text               mc_w.l(llshl.o)
    0x0800150e   0x0800150e   0x00000020   Code   RO         5246    .text               mc_w.l(llushr.o)
    0x0800152e   0x0800152e   0x00000024   Code   RO         5248    .text               mc_w.l(llsshr.o)
    0x08001552   0x08001552   0x00000000   Code   RO         5263    .text               mc_w.l(iusefp.o)
    0x08001552   0x08001552   0x0000006e   Code   RO         5264    .text               mf_w.l(fepilogue.o)
    0x080015c0   0x080015c0   0x000000ba   Code   RO         5266    .text               mf_w.l(depilogue.o)
    0x0800167a   0x0800167a   0x00000002   PAD
    0x0800167c   0x0800167c   0x00000088   Code   RO         5268    .text               mf_w.l(drnd.o)
    0x08001704   0x08001704   0x00000030   Code   RO         5270    .text               mf_w.l(dfixul.o)
    0x08001734   0x08001734   0x00000030   Code   RO         5272    .text               mf_w.l(cdrcmple.o)
    0x08001764   0x08001764   0x00000024   Code   RO         5274    .text               mc_w.l(init.o)
    0x08001788   0x08001788   0x0000000c   Code   RO          515    i.AD9833_Delay      ad9833.o
    0x08001794   0x08001794   0x00000006   Code   RO          518    i.AD9833_SetPhase_No1  ad9833.o
    0x0800179a   0x0800179a   0x00000006   Code   RO          519    i.AD9833_SetPhase_No2  ad9833.o
    0x080017a0   0x080017a0   0x000000b0   Code   RO          520    i.AD9833_WaveSeting_No1  ad9833.o
    0x08001850   0x08001850   0x000000b0   Code   RO          521    i.AD9833_WaveSeting_No2  ad9833.o
    0x08001900   0x08001900   0x00000070   Code   RO          522    i.AD9833_Write_No1  ad9833.o
    0x08001970   0x08001970   0x00000070   Code   RO          523    i.AD9833_Write_No2  ad9833.o
    0x080019e0   0x080019e0   0x0000006e   Code   RO         1404    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x08001a4e   0x08001a4e   0x00000016   Code   RO         1405    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x08001a64   0x08001a64   0x0000000a   Code   RO         1406    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08001a6e   0x08001a6e   0x00000002   PAD
    0x08001a70   0x08001a70   0x00000128   Code   RO         1407    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08001b98   0x08001b98   0x00000002   Code   RO         1117    i.BusFault_Handler  stm32f4xx_it.o
    0x08001b9a   0x08001b9a   0x00000002   PAD
    0x08001b9c   0x08001b9c   0x0000000c   Code   RO         1118    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x08001ba8   0x08001ba8   0x0000000c   Code   RO         1119    i.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08001bb4   0x08001bb4   0x00000028   Code   RO         2141    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08001bdc   0x08001bdc   0x00000054   Code   RO         2142    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08001c30   0x08001c30   0x00000028   Code   RO         2143    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08001c58   0x08001c58   0x0000005c   Code   RO         1323    i.DPLL_Simple_ApplyCorrection  dpll_simple.o
    0x08001cb4   0x08001cb4   0x0000000c   Code   RO         1324    i.DPLL_Simple_Enable  dpll_simple.o
    0x08001cc0   0x08001cc0   0x0000002c   Code   RO         1325    i.DPLL_Simple_GetPhaseError  dpll_simple.o
    0x08001cec   0x08001cec   0x00000040   Code   RO         1326    i.DPLL_Simple_Init  dpll_simple.o
    0x08001d2c   0x08001d2c   0x0000001c   Code   RO         1327    i.DPLL_Simple_IsLocked  dpll_simple.o
    0x08001d48   0x08001d48   0x00000088   Code   RO         1328    i.DPLL_Simple_PhaseDetect  dpll_simple.o
    0x08001dd0   0x08001dd0   0x00000030   Code   RO         1330    i.DPLL_Simple_SetPhaseOffset  dpll_simple.o
    0x08001e00   0x08001e00   0x00000130   Code   RO         1331    i.DPLL_Simple_Update  dpll_simple.o
    0x08001f30   0x08001f30   0x00000002   Code   RO         1120    i.DebugMon_Handler  stm32f4xx_it.o
    0x08001f32   0x08001f32   0x00000018   Code   RO          602    i.Delay_1us         si5351a.o
    0x08001f4a   0x08001f4a   0x00000004   Code   RO          778    i.Error_Handler     main.o
    0x08001f4e   0x08001f4e   0x00000002   PAD
    0x08001f50   0x08001f50   0x0000003c   Code   RO         2822    i.FSMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08001f8c   0x08001f8c   0x00000068   Code   RO         2823    i.FSMC_NORSRAM_Init  stm32f4xx_ll_fsmc.o
    0x08001ff4   0x08001ff4   0x00000044   Code   RO         2824    i.FSMC_NORSRAM_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08002038   0x08002038   0x0000014c   Code   RO         1409    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08002184   0x08002184   0x00000018   Code   RO          779    i.HAL_ADC_ConvCpltCallback  main.o
    0x0800219c   0x0800219c   0x00000002   Code   RO         1411    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x0800219e   0x0800219e   0x00000002   Code   RO         1413    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x080021a0   0x080021a0   0x00000054   Code   RO         1418    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x080021f4   0x080021f4   0x00000100   Code   RO          890    i.HAL_ADC_MspInit   adc.o
    0x080022f4   0x080022f4   0x00000154   Code   RO         1425    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08002448   0x08002448   0x000001a0   Code   RO         2149    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x080025e8   0x080025e8   0x000000d4   Code   RO         2150    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080026bc   0x080026bc   0x0000006e   Code   RO         2154    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800272a   0x0800272a   0x00000002   PAD
    0x0800272c   0x0800272c   0x00000024   Code   RO         2563    i.HAL_Delay         stm32f4xx_hal.o
    0x08002750   0x08002750   0x000000a8   Code   RO          961    i.HAL_FSMC_MspInit  fsmc.o
    0x080027f8   0x080027f8   0x000001f0   Code   RO         2037    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080029e8   0x080029e8   0x0000000a   Code   RO         2039    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x080029f2   0x080029f2   0x0000000a   Code   RO         2041    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080029fc   0x080029fc   0x0000000c   Code   RO         2569    i.HAL_GetTick       stm32f4xx_hal.o
    0x08002a08   0x08002a08   0x00000010   Code   RO         2575    i.HAL_IncTick       stm32f4xx_hal.o
    0x08002a18   0x08002a18   0x00000034   Code   RO         2576    i.HAL_Init          stm32f4xx_hal.o
    0x08002a4c   0x08002a4c   0x00000040   Code   RO         2577    i.HAL_InitTick      stm32f4xx_hal.o
    0x08002a8c   0x08002a8c   0x00000030   Code   RO         1217    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002abc   0x08002abc   0x0000001a   Code   RO         2426    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002ad6   0x08002ad6   0x00000002   PAD
    0x08002ad8   0x08002ad8   0x00000040   Code   RO         2432    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002b18   0x08002b18   0x00000024   Code   RO         2433    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002b3c   0x08002b3c   0x00000134   Code   RO         1683    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002c70   0x08002c70   0x00000020   Code   RO         1690    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002c90   0x08002c90   0x00000020   Code   RO         1691    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002cb0   0x08002cb0   0x00000060   Code   RO         1692    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08002d10   0x08002d10   0x0000036c   Code   RO         1695    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800307c   0x0800307c   0x0000005c   Code   RO         2962    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x080030d8   0x080030d8   0x00000004   Code   RO          963    i.HAL_SRAM_MspInit  fsmc.o
    0x080030dc   0x080030dc   0x00000028   Code   RO         2437    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003104   0x08003104   0x00000002   Code   RO         3835    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08003106   0x08003106   0x00000002   Code   RO         3836    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08003108   0x08003108   0x00000090   Code   RO         3854    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003198   0x08003198   0x0000005a   Code   RO         3131    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080031f2   0x080031f2   0x00000002   PAD
    0x080031f4   0x080031f4   0x000000bc   Code   RO         1010    i.HAL_TIM_Base_MspInit  tim.o
    0x080032b0   0x080032b0   0x00000078   Code   RO         3134    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08003328   0x08003328   0x00000080   Code   RO         3136    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x080033a8   0x080033a8   0x00000026   Code   RO         3137    i.HAL_TIM_Base_Stop  stm32f4xx_hal_tim.o
    0x080033ce   0x080033ce   0x000000dc   Code   RO         3140    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x080034aa   0x080034aa   0x00000002   PAD
    0x080034ac   0x080034ac   0x00000038   Code   RO          780    i.HAL_TIM_IC_CaptureCallback  main.o
    0x080034e4   0x080034e4   0x0000011e   Code   RO         3167    i.HAL_TIM_IC_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003602   0x08003602   0x0000005a   Code   RO         3170    i.HAL_TIM_IC_Init   stm32f4xx_hal_tim.o
    0x0800365c   0x0800365c   0x00000002   Code   RO         3172    i.HAL_TIM_IC_MspInit  stm32f4xx_hal_tim.o
    0x0800365e   0x0800365e   0x00000002   PAD
    0x08003660   0x08003660   0x0000010c   Code   RO         3175    i.HAL_TIM_IC_Start_IT  stm32f4xx_hal_tim.o
    0x0800376c   0x0800376c   0x00000166   Code   RO         3179    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x080038d2   0x080038d2   0x00000002   Code   RO         3182    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x080038d4   0x080038d4   0x00000002   Code   RO         3209    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x080038d6   0x080038d6   0x00000002   PAD
    0x080038d8   0x080038d8   0x000000c4   Code   RO          740    i.HAL_TIM_PeriodElapsedCallback  key.o
    0x0800399c   0x0800399c   0x0000002a   Code   RO         3219    i.HAL_TIM_ReadCapturedValue  stm32f4xx_hal_tim.o
    0x080039c6   0x080039c6   0x00000056   Code   RO         3220    i.HAL_TIM_SlaveConfigSynchro  stm32f4xx_hal_tim.o
    0x08003a1c   0x08003a1c   0x00000002   Code   RO         3222    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08003a1e   0x08003a1e   0x00000062   Code   RO         4131    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08003a80   0x08003a80   0x0000006c   Code   RO         1064    i.HAL_UART_MspInit  usart.o
    0x08003aec   0x08003aec   0x000000b2   Code   RO         4139    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08003b9e   0x08003b9e   0x00000002   Code   RO         1121    i.HardFault_Handler  stm32f4xx_it.o
    0x08003ba0   0x08003ba0   0x00000054   Code   RO          606    i.I2C_SendByte      si5351a.o
    0x08003bf4   0x08003bf4   0x0000006c   Code   RO          607    i.I2C_Start         si5351a.o
    0x08003c60   0x08003c60   0x00000040   Code   RO          608    i.I2C_Stop          si5351a.o
    0x08003ca0   0x08003ca0   0x00000074   Code   RO          609    i.I2C_WaitAck       si5351a.o
    0x08003d14   0x08003d14   0x00000064   Code   RO          891    i.MX_ADC1_Init      adc.o
    0x08003d78   0x08003d78   0x00000064   Code   RO          892    i.MX_ADC3_Init      adc.o
    0x08003ddc   0x08003ddc   0x0000003c   Code   RO          937    i.MX_DMA_Init       dma.o
    0x08003e18   0x08003e18   0x00000088   Code   RO          964    i.MX_FSMC_Init      fsmc.o
    0x08003ea0   0x08003ea0   0x00000110   Code   RO          865    i.MX_GPIO_Init      gpio.o
    0x08003fb0   0x08003fb0   0x00000068   Code   RO         1011    i.MX_TIM2_Init      tim.o
    0x08004018   0x08004018   0x000000b8   Code   RO         1012    i.MX_TIM3_Init      tim.o
    0x080040d0   0x080040d0   0x00000068   Code   RO         1013    i.MX_TIM8_Init      tim.o
    0x08004138   0x08004138   0x00000038   Code   RO         1065    i.MX_USART1_UART_Init  usart.o
    0x08004170   0x08004170   0x00000002   Code   RO         1122    i.MemManage_Handler  stm32f4xx_it.o
    0x08004172   0x08004172   0x00000002   Code   RO         1123    i.NMI_Handler       stm32f4xx_it.o
    0x08004174   0x08004174   0x00000002   Code   RO         1124    i.PendSV_Handler    stm32f4xx_it.o
    0x08004176   0x08004176   0x00000002   Code   RO         1125    i.SVC_Handler       stm32f4xx_it.o
    0x08004178   0x08004178   0x00000004   Code   RO         1126    i.SysTick_Handler   stm32f4xx_it.o
    0x0800417c   0x0800417c   0x00000098   Code   RO          781    i.SystemClock_Config  main.o
    0x08004214   0x08004214   0x00000010   Code   RO         4460    i.SystemInit        system_stm32f4xx.o
    0x08004224   0x08004224   0x0000000c   Code   RO         1127    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08004230   0x08004230   0x0000000c   Code   RO         1128    i.TIM3_IRQHandler   stm32f4xx_it.o
    0x0800423c   0x0800423c   0x000000c8   Code   RO         3224    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08004304   0x08004304   0x0000001a   Code   RO         3225    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x0800431e   0x0800431e   0x00000014   Code   RO         3235    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08004332   0x08004332   0x00000010   Code   RO         3236    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08004342   0x08004342   0x0000008c   Code   RO         3241    i.TIM_SlaveTimer_SetConfig  stm32f4xx_hal_tim.o
    0x080043ce   0x080043ce   0x00000022   Code   RO         3242    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080043f0   0x080043f0   0x00000080   Code   RO         3243    i.TIM_TI1_SetConfig  stm32f4xx_hal_tim.o
    0x08004470   0x08004470   0x00000024   Code   RO         3244    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08004494   0x08004494   0x00000036   Code   RO         3245    i.TIM_TI2_SetConfig  stm32f4xx_hal_tim.o
    0x080044ca   0x080044ca   0x00000002   PAD
    0x080044cc   0x080044cc   0x0000010c   Code   RO         4157    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080045d8   0x080045d8   0x00000078   Code   RO         4160    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08004650   0x08004650   0x00000002   Code   RO         1129    i.UsageFault_Handler  stm32f4xx_it.o
    0x08004652   0x08004652   0x00000002   PAD
    0x08004654   0x08004654   0x00000020   Code   RO         5132    i.__0printf         mc_w.l(printfa.o)
    0x08004674   0x08004674   0x00000028   Code   RO         5134    i.__0sprintf        mc_w.l(printfa.o)
    0x0800469c   0x0800469c   0x00000020   Code   RO         2439    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080046bc   0x080046bc   0x00000004   PAD
    0x080046c0   0x080046c0   0x000000e0   Code   RO         4864    i.__hardfp_round    m_wm.l(round.o)
    0x080047a0   0x080047a0   0x0000000e   Code   RO         5278    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080047ae   0x080047ae   0x00000002   Code   RO         5279    i.__scatterload_null  mc_w.l(handlers.o)
    0x080047b0   0x080047b0   0x0000000e   Code   RO         5280    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080047be   0x080047be   0x00000002   PAD
    0x080047c0   0x080047c0   0x00000184   Code   RO         5139    i._fp_digits        mc_w.l(printfa.o)
    0x08004944   0x08004944   0x000006b4   Code   RO         5140    i._printf_core      mc_w.l(printfa.o)
    0x08004ff8   0x08004ff8   0x00000024   Code   RO         5141    i._printf_post_padding  mc_w.l(printfa.o)
    0x0800501c   0x0800501c   0x0000002e   Code   RO         5142    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800504a   0x0800504a   0x0000000a   Code   RO         5144    i._sputc            mc_w.l(printfa.o)
    0x08005054   0x08005054   0x00000018   Code   RO         1067    i.fputc             usart.o
    0x0800506c   0x0800506c   0x00000050   Code   RO         5160    i.free              mc_w.l(malloc.o)
    0x080050bc   0x080050bc   0x00000034   Code   RO          612    i.i2cSendRegister   si5351a.o
    0x080050f0   0x080050f0   0x00000030   Code   RO           13    i.lcd_clear         lcd.o
    0x08005120   0x08005120   0x000000ec   Code   RO           15    i.lcd_display_dir   lcd.o
    0x0800520c   0x0800520c   0x00000016   Code   RO           21    i.lcd_draw_point    lcd.o
    0x08005222   0x08005222   0x0000022e   Code   RO           23    i.lcd_ex_ili9341_reginit  lcd.o
    0x08005450   0x08005450   0x00000342   Code   RO           24    i.lcd_ex_ili9806_reginit  lcd.o
    0x08005792   0x08005792   0x00000ef4   Code   RO           25    i.lcd_ex_nt35310_reginit  lcd.o
    0x08006686   0x08006686   0x00000efa   Code   RO           26    i.lcd_ex_nt35510_reginit  lcd.o
    0x08007580   0x08007580   0x00000170   Code   RO           27    i.lcd_ex_ssd1963_reginit  lcd.o
    0x080076f0   0x080076f0   0x000001aa   Code   RO           28    i.lcd_ex_st7789_reginit  lcd.o
    0x0800789a   0x0800789a   0x000001c8   Code   RO           29    i.lcd_ex_st7796_reginit  lcd.o
    0x08007a62   0x08007a62   0x00000002   PAD
    0x08007a64   0x08007a64   0x0000027c   Code   RO           32    i.lcd_init          lcd.o
    0x08007ce0   0x08007ce0   0x0000001a   Code   RO           34    i.lcd_rd_data       lcd.o
    0x08007cfa   0x08007cfa   0x00000002   PAD
    0x08007cfc   0x08007cfc   0x00000184   Code   RO           36    i.lcd_scan_dir      lcd.o
    0x08007e80   0x08007e80   0x000000f4   Code   RO           37    i.lcd_set_cursor    lcd.o
    0x08007f74   0x08007f74   0x000000dc   Code   RO           39    i.lcd_show_char     lcd.o
    0x08008050   0x08008050   0x00000056   Code   RO           41    i.lcd_show_string   lcd.o
    0x080080a6   0x080080a6   0x00000002   PAD
    0x080080a8   0x080080a8   0x00000050   Code   RO           43    i.lcd_ssd_backlight_set  lcd.o
    0x080080f8   0x080080f8   0x00000018   Code   RO           44    i.lcd_wr_data       lcd.o
    0x08008110   0x08008110   0x00000018   Code   RO           45    i.lcd_wr_regno      lcd.o
    0x08008128   0x08008128   0x00000014   Code   RO           46    i.lcd_write_ram_prepare  lcd.o
    0x0800813c   0x0800813c   0x0000000c   Code   RO           47    i.lcd_write_reg     lcd.o
    0x08008148   0x08008148   0x000008a0   Code   RO          782    i.main              main.o
    0x080089e8   0x080089e8   0x0000006c   Code   RO         5161    i.malloc            mc_w.l(malloc.o)
    0x08008a54   0x08008a54   0x0000007c   Code   RO          386    i.modeFilter        calculate.o
    0x08008ad0   0x08008ad0   0x00000100   Code   RO          741    i.phase_set         key.o
    0x08008bd0   0x08008bd0   0x0000006c   Code   RO          613    i.setupMultisynth   si5351a.o
    0x08008c3c   0x08008c3c   0x000000c4   Code   RO          614    i.setupPLL          si5351a.o
    0x08008d00   0x08008d00   0x000000bc   Code   RO          615    i.si5351aSetFrequency  si5351a.o
    0x08008dbc   0x08008dbc   0x00002f80   Data   RO           49    .constdata          lcd.o
    0x0800bd3c   0x0800bd3c   0x00000008   Data   RO         2156    .constdata          stm32f4xx_hal_dma.o
    0x0800bd44   0x0800bd44   0x00000010   Data   RO         4461    .constdata          system_stm32f4xx.o
    0x0800bd54   0x0800bd54   0x00000008   Data   RO         4462    .constdata          system_stm32f4xx.o
    0x0800bd5c   0x0800bd5c   0x00000010   Data   RO         4604    .constdata          arm_cortexM4lf_math.lib(arm_const_structs.o)
    0x0800bd6c   0x0800bd6c   0x00002000   Data   RO         4768    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800dd6c   0x0800dd6c   0x00000e10   Data   RO         4797    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800eb7c   0x0800eb7c   0x00000804   Data   RO         4817    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800f380   0x0800f380   0x00000020   Data   RO         5276    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800f3a0, Size: 0x00004230, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800f3a0   0x00000004   Data   RW           51    .data               lcd.o
    0x20000004   0x0800f3a4   0x0000000c   Data   RW          743    .data               key.o
    0x20000010   0x0800f3b0   0x00000068   Data   RW          784    .data               main.o
    0x20000078   0x0800f418   0x00000008   Data   RW          966    .data               fsmc.o
    0x20000080   0x0800f420   0x00000001   Data   RW         1333    .data               dpll_simple.o
    0x20000081   0x0800f421   0x00000003   PAD
    0x20000084   0x0800f424   0x0000000c   Data   RW         2583    .data               stm32f4xx_hal.o
    0x20000090   0x0800f430   0x00000004   Data   RW         4463    .data               system_stm32f4xx.o
    0x20000094   0x0800f434   0x00000004   Data   RW         5241    .data               mc_w.l(stdout.o)
    0x20000098   0x0800f438   0x00000004   Data   RW         5254    .data               mc_w.l(mvars.o)
    0x2000009c   0x0800f43c   0x00000004   Data   RW         5255    .data               mc_w.l(mvars.o)
    0x200000a0        -       0x0000005e   Zero   RW           48    .bss                lcd.o
    0x200000fe        -       0x0000000f   Zero   RW          742    .bss                key.o
    0x2000010d   0x0800f440   0x00000003   PAD
    0x20000110        -       0x00003838   Zero   RW          783    .bss                main.o
    0x20003948        -       0x00000150   Zero   RW          893    .bss                adc.o
    0x20003a98        -       0x00000050   Zero   RW          965    .bss                fsmc.o
    0x20003ae8        -       0x000000d8   Zero   RW         1014    .bss                tim.o
    0x20003bc0        -       0x00000044   Zero   RW         1068    .bss                usart.o
    0x20003c04        -       0x00000028   Zero   RW         1332    .bss                dpll_simple.o
    0x20003c2c   0x0800f440   0x00000004   PAD
    0x20003c30        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20003e30        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800f440, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       600         40          0          0          0       5830   ad9833.o
       456         44          0          0        336       2802   adc.o
       124          0          0          0          0       2249   calculate.o
        60          4          0          0          0        862   dma.o
       728        118          0          1         40       6551   dpll_simple.o
       308         32          0          8         80       2483   fsmc.o
       272         18          0          0          0       1219   gpio.o
       452         42          0         12         15       2984   key.o
     12370         94      12160          4         94     744815   lcd.o
      2444        364          0        104      14392       7818   main.o
       940         50          0          0          0       6673   si5351a.o
        36          8        392          0       1536        872   startup_stm32f407xx.o
       180         28          0         12          0       9729   stm32f4xx_hal.o
      1198         66          0          0          0       7298   stm32f4xx_hal_adc.o
       198         14          0          0          0      34183   stm32f4xx_hal_cortex.o
       902         16          8          0          0       6083   stm32f4xx_hal_dma.o
       516         46          0          0          0       3011   stm32f4xx_hal_gpio.o
        48          6          0          0          0        934   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5480   stm32f4xx_hal_rcc.o
        92          0          0          0          0       1300   stm32f4xx_hal_sram.o
      2388        166          0          0          0      20118   stm32f4xx_hal_tim.o
       148         28          0          0          0       2613   stm32f4xx_hal_tim_ex.o
       664         10          0          0          0       4404   stm32f4xx_hal_uart.o
        68         24          0          0          0       6566   stm32f4xx_it.o
       232         14          0          0          0       4107   stm32f4xx_ll_fsmc.o
        16          4         24          4          0       1247   system_stm32f4xx.o
       580         40          0          0        216       3600   tim.o
       188         26          0          0         68       2518   usart.o

    ----------------------------------------------------------------------
     27584       <USER>      <GROUP>        148      16784     898349   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          0          3          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       192          0          0          0          0        572   arm_bitreversal2.o
      1826          0          0          0          0       4025   arm_cfft_f32.o
      1244          4          0          0          0       3123   arm_cfft_radix8_f32.o
       252          6          0          0          0      14588   arm_cmplx_mag_f32.o
         0          0      13844          0          0       4704   arm_common_tables.o
         0          0         16          0          0       4212   arm_const_structs.o
       148         18          0          0          0      17238   arm_sin_f32.o
       224         30          0          0          0        172   round.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
      2268         96          0          0          0        616   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        50          0          0          0          0         76   dfixui.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
       136          4          0          0          0         92   drnd.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      8308        <USER>      <GROUP>         12          0      51334   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3662         28      13860          0          0      48462   arm_cortexM4lf_math.lib
       224         30          0          0          0        172   m_wm.l
      2822        132          0         12          0       1328   mc_w.l
      1592          4          0          0          0       1372   mf_w.l

    ----------------------------------------------------------------------
      8308        <USER>      <GROUP>         12          0      51334   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     35892       1568      26476        160      16784     933983   Grand Totals
     35892       1568      26476        160      16784     933983   ELF Image Totals
     35892       1568      26476        160          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                62368 (  60.91kB)
    Total RW  Size (RW Data + ZI Data)             16944 (  16.55kB)
    Total ROM Size (Code + RO Data + RW Data)      62528 (  61.06kB)

==============================================================================

