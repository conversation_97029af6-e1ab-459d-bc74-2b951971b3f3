#include "calculate.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

//中值滤波器
uint16_t medianFilter(uint16_t *window, int size) {
    int temp;
    // 对窗口数据进行排序
    for (int i = 0; i < size - 1; i++) {
        for (int j = i + 1; j < size; j++) {
            if (window[i] > window[j]) {
                temp = window[i];
                window[i] = window[j];
                window[j] = temp;
            }
        }
    }
    // 返回中值
    return window[size / 2];
}

 // 计算窗口中出现次数最多的最大数据
uint16_t modeFilter(uint16_t *window, int size) {
    int count[size]; // 用于记录每个值的出现次数
    int maxCount = 0;  // 最大出现次数
    uint16_t modeValue = 0; // 最终的返回值

    // 初始化计数数组
    for (int i = 0; i < size; i++) {
        count[i] = 0;
    }

    // 统计每个元素的出现次数，忽略0
    for (int i = 0; i < size; i++) {
        if (window[i] != 0) {  // 忽略0
            for (int j = 0; j < size; j++) {
                if (window[i] == window[j]) {
                    count[i]++;
                }
            }
            // 更新最大出现次数和对应的值
            if (count[i] > maxCount) {
                maxCount = count[i];
                modeValue = window[i];
            } else if (count[i] == maxCount) {
                // 如果有多个值有相同的最大次数，选择最大值
                if (window[i] > modeValue) {
                    modeValue = window[i];
                }
            }
        }
    }

    return modeValue;
}

/**
 * @brief 数字锁相环初始化
 * @param dpll: 数字锁相环结构体指针
 * @param target_freq: 目标频率
 */
void DPLL_Init(DPLL_TypeDef *dpll, float target_freq) {
    dpll->input_freq = target_freq;
    dpll->output_freq = target_freq;
    dpll->phase_error = 0.0f;
    dpll->phase_correction = 0.0f;
    dpll->frequency_error = 0.0f;
    dpll->is_locked = false;
    dpll->lock_time = 0;
}

/**
 * @brief 生成正余弦查找表
 * @param sin_table: 正弦表指针
 * @param cos_table: 余弦表指针
 * @param length: 表长度
 * @param freq: 频率
 * @param sample_rate: 采样率
 */
void GenerateSinCosTable(float *sin_table, float *cos_table, uint16_t length,
                        float freq, float sample_rate) {
    float omega = 2.0f * M_PI * freq / sample_rate;

    for (uint16_t i = 0; i < length; i++) {
        sin_table[i] = sinf(omega * i);
        cos_table[i] = cosf(omega * i);
    }
}

/**
 * @brief 单频率DFT计算
 * @param input: 输入信号
 * @param length: 信号长度
 * @param freq: 目标频率
 * @param sample_rate: 采样率
 * @param sin_component: 正弦分量输出
 * @param cos_component: 余弦分量输出
 */
void DFT_SingleFreq(float *input, uint16_t length, float freq, float sample_rate,
                   float *sin_component, float *cos_component) {
    float omega = 2.0f * M_PI * freq / sample_rate;
    float sin_sum = 0.0f;
    float cos_sum = 0.0f;

    for (uint16_t i = 0; i < length; i++) {
        float sin_val = sinf(omega * i);
        float cos_val = cosf(omega * i);

        sin_sum += input[i] * sin_val;
        cos_sum += input[i] * cos_val;
    }

    *sin_component = sin_sum / length;
    *cos_component = cos_sum / length;
}

/**
 * @brief 计算幅度和相位
 * @param sin_comp: 正弦分量
 * @param cos_comp: 余弦分量
 * @param magnitude: 幅度输出
 * @param phase: 相位输出
 */
void DFT_PhaseCalculation(float sin_comp, float cos_comp, float *magnitude, float *phase) {
    *magnitude = sqrtf(sin_comp * sin_comp + cos_comp * cos_comp);
    *phase = atan2f(sin_comp, cos_comp);
}

/**
 * @brief 相位检测器
 * @param pd: 相位检测器结构体指针
 * @param input_signal: 输入信号
 * @param reference_signal: 参考信号
 * @param length: 信号长度
 */
void DPLL_PhaseDetect(PhaseDetector_TypeDef *pd, float *input_signal, float *reference_signal, uint16_t length) {
    float sin_sum = 0.0f;
    float cos_sum = 0.0f;

    // 计算输入信号与参考信号的相关性
    for (uint16_t i = 0; i < length; i++) {
        sin_sum += input_signal[i] * reference_signal[i];
        cos_sum += input_signal[i] * reference_signal[(i + length/4) % length]; // 90度相移
    }

    pd->sin_component = sin_sum / length;
    pd->cos_component = cos_sum / length;

    // 计算幅度和相位
    DFT_PhaseCalculation(pd->sin_component, pd->cos_component, &pd->magnitude, &pd->phase);
}

/**
 * @brief 计算相位误差
 * @param input_pd: 输入信号相位检测器
 * @param ref_pd: 参考信号相位检测器
 * @return 相位误差（弧度）
 */
float DPLL_CalculatePhaseError(PhaseDetector_TypeDef *input_pd, PhaseDetector_TypeDef *ref_pd) {
    float phase_diff = input_pd->phase - ref_pd->phase;

    // 将相位差限制在[-π, π]范围内
    while (phase_diff > M_PI) {
        phase_diff -= 2.0f * M_PI;
    }
    while (phase_diff < -M_PI) {
        phase_diff += 2.0f * M_PI;
    }

    return phase_diff;
}

/**
 * @brief 频率校正
 * @param dpll: 数字锁相环结构体指针
 * @param freq_error: 频率误差
 */
void DPLL_FrequencyCorrection(DPLL_TypeDef *dpll, float freq_error) {
    const float freq_gain = 0.1f; // 频率环路增益

    dpll->frequency_error = freq_error;
    dpll->output_freq += freq_gain * freq_error;

    // 限制输出频率范围
    if (dpll->output_freq < DPLL_FREQ_START) {
        dpll->output_freq = DPLL_FREQ_START;
    } else if (dpll->output_freq > DPLL_FREQ_END) {
        dpll->output_freq = DPLL_FREQ_END;
    }
}

/**
 * @brief 相位校正
 * @param dpll: 数字锁相环结构体指针
 * @param phase_error: 相位误差
 */
void DPLL_PhaseCorrection(DPLL_TypeDef *dpll, float phase_error) {
    const float phase_gain = 0.5f; // 相位环路增益

    dpll->phase_error = phase_error;
    dpll->phase_correction += phase_gain * phase_error;

    // 将相位校正值限制在[0, 2π]范围内
    while (dpll->phase_correction >= 2.0f * M_PI) {
        dpll->phase_correction -= 2.0f * M_PI;
    }
    while (dpll->phase_correction < 0.0f) {
        dpll->phase_correction += 2.0f * M_PI;
    }
}

/**
 * @brief 判断锁相环是否锁定
 * @param dpll: 数字锁相环结构体指针
 * @param threshold: 锁定阈值
 * @return 锁定状态
 */
bool DPLL_IsLocked(DPLL_TypeDef *dpll, float threshold) {
    float abs_phase_error = fabsf(dpll->phase_error);
    float abs_freq_error = fabsf(dpll->frequency_error);

    bool phase_locked = abs_phase_error < threshold;
    bool freq_locked = abs_freq_error < (threshold * 100.0f); // 频率阈值更宽松

    if (phase_locked && freq_locked) {
        dpll->lock_time++;
        if (dpll->lock_time > 10) { // 连续10次满足条件才认为锁定
            dpll->is_locked = true;
        }
    } else {
        dpll->lock_time = 0;
        dpll->is_locked = false;
    }

    return dpll->is_locked;
}

/**
 * @brief 数字锁相环主更新函数
 * @param dpll: 数字锁相环结构体指针
 * @param input_signal: 输入信号数组
 * @param length: 信号长度
 */
void DPLL_Update(DPLL_TypeDef *dpll, float *input_signal, uint16_t length) {
    // 生成本地参考信号
    static float reference_signal[DPLL_SAMPLE_LENGTH];
    static float sin_table[DPLL_SAMPLE_LENGTH];
    static float cos_table[DPLL_SAMPLE_LENGTH];

    // 生成当前输出频率的正余弦表
    GenerateSinCosTable(sin_table, cos_table, length, dpll->output_freq, DPLL_SAMPLE_RATE);

    // 应用相位校正生成参考信号
    for (uint16_t i = 0; i < length; i++) {
        float phase_offset = dpll->phase_correction;
        reference_signal[i] = sinf(2.0f * M_PI * dpll->output_freq * i / DPLL_SAMPLE_RATE + phase_offset);
    }

    // 相位检测
    PhaseDetector_TypeDef input_pd, ref_pd;

    // 计算输入信号的相位信息
    float sin_comp, cos_comp;
    DFT_SingleFreq(input_signal, length, dpll->input_freq, DPLL_SAMPLE_RATE, &sin_comp, &cos_comp);
    DFT_PhaseCalculation(sin_comp, cos_comp, &input_pd.magnitude, &input_pd.phase);

    // 计算参考信号的相位信息
    DFT_SingleFreq(reference_signal, length, dpll->output_freq, DPLL_SAMPLE_RATE, &sin_comp, &cos_comp);
    DFT_PhaseCalculation(sin_comp, cos_comp, &ref_pd.magnitude, &ref_pd.phase);

    // 计算相位误差
    float phase_error = DPLL_CalculatePhaseError(&input_pd, &ref_pd);

    // 计算频率误差（基于相位误差的变化率）
    static float last_phase_error = 0.0f;
    float freq_error = (phase_error - last_phase_error) * DPLL_SAMPLE_RATE / (2.0f * M_PI * length);
    last_phase_error = phase_error;

    // 环路滤波和校正
    DPLL_FrequencyCorrection(dpll, freq_error);
    DPLL_PhaseCorrection(dpll, phase_error);

    // 检查锁定状态
    DPLL_IsLocked(dpll, 0.1f); // 0.1弧度的锁定阈值
}

/**
 * @brief 将相位从弧度转换为度
 * @param phase_rad: 弧度值
 * @return 度数值
 */
float DPLL_RadToDeg(float phase_rad) {
    return phase_rad * 180.0f / M_PI;
}

/**
 * @brief 将相位从度转换为弧度
 * @param phase_deg: 度数值
 * @return 弧度值
 */
float DPLL_DegToRad(float phase_deg) {
    return phase_deg * M_PI / 180.0f;
}

/**
 * @brief 获取相位校正的定时器延迟值
 * @param phase_correction: 相位校正值（弧度）
 * @param frequency: 信号频率
 * @param timer_freq: 定时器频率
 * @return 定时器延迟值
 */
uint32_t DPLL_GetTimerDelay(float phase_correction, float frequency, uint32_t timer_freq) {
    // 将相位转换为时间延迟
    float time_delay = phase_correction / (2.0f * M_PI * frequency);

    // 转换为定时器计数值
    uint32_t timer_delay = (uint32_t)(time_delay * timer_freq);

    return timer_delay;
}