#ifndef __CALCULATE_H
#define __CALCULATE_H

#include "stdio.h"
#include "math.h"
#include "stdint.h"
#include "stdbool.h"
#include "arm_const_structs.h"
#include "arm_math.h"

// 数字锁相环相关定义
#define DPLL_SAMPLE_RATE        1000000.0f    // 1MSPS采样率
#define DPLL_SAMPLE_LENGTH      1000          // 采样长度
#define DPLL_MAX_FREQUENCIES    19            // 最大频率数量
#define DPLL_FREQ_START         10000.0f      // 起始频率10KHz
#define DPLL_FREQ_STEP          5000.0f       // 频率步进5KHz
#define DPLL_FREQ_END           100000.0f     // 结束频率100KHz

// 数字锁相环结构体
typedef struct {
    float input_freq;           // 输入信号频率
    float output_freq;          // 输出信号频率
    float phase_error;          // 相位误差
    float phase_correction;     // 相位校正值
    float frequency_error;      // 频率误差
    bool is_locked;            // 锁定状态
    uint32_t lock_time;        // 锁定时间
} DPLL_TypeDef;

// 相位检测结构体
typedef struct {
    float sin_component;        // 正弦分量
    float cos_component;        // 余弦分量
    float magnitude;           // 幅度
    float phase;               // 相位
} PhaseDetector_TypeDef;

// 滤波器函数
uint16_t medianFilter(uint16_t *window, int size);
uint16_t modeFilter(uint16_t *window, int size);

// 数字锁相环函数
void DPLL_Init(DPLL_TypeDef *dpll, float target_freq);
void DPLL_PhaseDetect(PhaseDetector_TypeDef *pd, float *input_signal, float *reference_signal, uint16_t length);
float DPLL_CalculatePhaseError(PhaseDetector_TypeDef *input_pd, PhaseDetector_TypeDef *ref_pd);
void DPLL_FrequencyCorrection(DPLL_TypeDef *dpll, float freq_error);
void DPLL_PhaseCorrection(DPLL_TypeDef *dpll, float phase_error);
bool DPLL_IsLocked(DPLL_TypeDef *dpll, float threshold);
void DPLL_Update(DPLL_TypeDef *dpll, float *input_signal, uint16_t length);

// DFT相关函数
void DFT_SingleFreq(float *input, uint16_t length, float freq, float sample_rate,
                   float *sin_component, float *cos_component);
void DFT_PhaseCalculation(float sin_comp, float cos_comp, float *magnitude, float *phase);

// 正余弦表生成函数
void GenerateSinCosTable(float *sin_table, float *cos_table, uint16_t length,
                        float freq, float sample_rate);

// 辅助函数
float DPLL_RadToDeg(float phase_rad);
float DPLL_DegToRad(float phase_deg);
uint32_t DPLL_GetTimerDelay(float phase_correction, float frequency, uint32_t timer_freq);

#endif
