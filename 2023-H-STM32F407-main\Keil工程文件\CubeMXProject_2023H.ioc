#MicroXplorer Configuration settings - do not modify
ADC1.Channel-9\#ChannelRegularConversion=ADC_CHANNEL_6
ADC1.DMAContinuousRequests=ENABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T8_TRGO
ADC1.IPParameters=Rank-9\#ChannelRegularConversion,master,Channel-9\#ChannelRegularConversion,SamplingTime-9\#ChannelRegularConversion,NbrOfConversionFlag,ExternalTrigConv,DMAContinuousRequests
ADC1.NbrOfConversionFlag=1
ADC1.Rank-9\#ChannelRegularConversion=1
ADC1.SamplingTime-9\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
ADC3.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_8
ADC3.DMAContinuousRequests=ENABLE
ADC3.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T2_TRGO
ADC3.IPParameters=Rank-3\#ChannelRegularConversion,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,NbrOfConversionFlag,DMAContinuousRequests,ExternalTrigConv
ADC3.NbrOfConversionFlag=1
ADC3.Rank-3\#ChannelRegularConversion=1
ADC3.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_VERY_HIGH
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.ADC3.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC3.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC3.1.Instance=DMA2_Stream1
Dma.ADC3.1.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC3.1.MemInc=DMA_MINC_ENABLE
Dma.ADC3.1.Mode=DMA_CIRCULAR
Dma.ADC3.1.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC3.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC3.1.Priority=DMA_PRIORITY_VERY_HIGH
Dma.ADC3.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.Request1=ADC3
Dma.RequestsNb=2
FSMC.AddressSetupTime1=0x0F
FSMC.BusTurnAroundDuration1=0
FSMC.DataSetupTime1=60
FSMC.ExtendedAddressSetupTime1=9
FSMC.ExtendedBusTurnAroundDuration1=0
FSMC.ExtendedDataSetupTime1=9
FSMC.ExtendedMode1=FSMC_EXTENDED_MODE_ENABLE
FSMC.IPParameters=ExtendedMode1,AddressSetupTime1,BusTurnAroundDuration1,DataSetupTime1,ExtendedAddressSetupTime1,ExtendedDataSetupTime1,ExtendedBusTurnAroundDuration1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=ADC3
Mcu.IP10=USART1
Mcu.IP2=DMA
Mcu.IP3=FSMC
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SYS
Mcu.IP7=TIM2
Mcu.IP8=TIM3
Mcu.IP9=TIM8
Mcu.IPNb=11
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PF7
Mcu.Pin11=PF10
Mcu.Pin12=PH0-OSC_IN
Mcu.Pin13=PH1-OSC_OUT
Mcu.Pin14=PA6
Mcu.Pin15=PF12
Mcu.Pin16=PE7
Mcu.Pin17=PE8
Mcu.Pin18=PE9
Mcu.Pin19=PE10
Mcu.Pin2=PE4
Mcu.Pin20=PE11
Mcu.Pin21=PE12
Mcu.Pin22=PE13
Mcu.Pin23=PE14
Mcu.Pin24=PE15
Mcu.Pin25=PB15
Mcu.Pin26=PD8
Mcu.Pin27=PD9
Mcu.Pin28=PD10
Mcu.Pin29=PD14
Mcu.Pin3=PF0
Mcu.Pin30=PD15
Mcu.Pin31=PC6
Mcu.Pin32=PA9
Mcu.Pin33=PA10
Mcu.Pin34=PA13
Mcu.Pin35=PA14
Mcu.Pin36=PD0
Mcu.Pin37=PD1
Mcu.Pin38=PD4
Mcu.Pin39=PD5
Mcu.Pin4=PF1
Mcu.Pin40=PG12
Mcu.Pin41=VP_SYS_VS_Systick
Mcu.Pin42=VP_TIM2_VS_ClockSourceINT
Mcu.Pin43=VP_TIM3_VS_ControllerModeReset
Mcu.Pin44=VP_TIM3_VS_ClockSourceINT
Mcu.Pin45=VP_TIM8_VS_ClockSourceINT
Mcu.Pin5=PF2
Mcu.Pin6=PF3
Mcu.Pin7=PF4
Mcu.Pin8=PF5
Mcu.Pin9=PF6
Mcu.PinsNb=46
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407ZGTx
MxCube.Version=6.9.2
MxDb.Version=DB.6.0.92
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA6.Locked=true
PA6.Signal=ADCx_IN6
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=LCD_BL
PB15.Locked=true
PB15.Signal=GPIO_Output
PC6.GPIOParameters=GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=FRRQ_BIAS
PC6.GPIO_PuPd=GPIO_PULLDOWN
PC6.Signal=S_TIM3_CH1
PD0.Signal=FSMC_D2_DA2
PD1.Signal=FSMC_D3_DA3
PD10.Signal=FSMC_D15_DA15
PD14.Signal=FSMC_D0_DA0
PD15.Signal=FSMC_D1_DA1
PD4.Signal=FSMC_NOE
PD5.Signal=FSMC_NWE
PD8.Signal=FSMC_D13_DA13
PD9.Signal=FSMC_D14_DA14
PE10.Signal=FSMC_D7_DA7
PE11.Signal=FSMC_D8_DA8
PE12.Signal=FSMC_D9_DA9
PE13.Signal=FSMC_D10_DA10
PE14.Signal=FSMC_D11_DA11
PE15.Signal=FSMC_D12_DA12
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=KEY2
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=KEY1
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_Label
PE4.GPIO_Label=KEY0
PE4.Locked=true
PE4.Signal=GPIO_Input
PE7.Signal=FSMC_D4_DA4
PE8.Signal=FSMC_D5_DA5
PE9.Signal=FSMC_D6_DA6
PF0.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PF0.GPIO_Label=SI5351A_SCL
PF0.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PF0.GPIO_PuPd=GPIO_NOPULL
PF0.Locked=true
PF0.Signal=GPIO_Output
PF1.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PF1.GPIO_Label=SI5351A_SDA
PF1.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PF1.GPIO_PuPd=GPIO_NOPULL
PF1.Locked=true
PF1.Signal=GPIO_Output
PF10.Mode=IN8
PF10.Signal=ADC3_IN8
PF12.Signal=FSMC_A6
PF2.GPIOParameters=GPIO_Label
PF2.GPIO_Label=AD9833_FSYNC_1
PF2.Locked=true
PF2.Signal=GPIO_Output
PF3.GPIOParameters=GPIO_Label
PF3.GPIO_Label=AD9833_SCLK_1
PF3.Locked=true
PF3.Signal=GPIO_Output
PF4.GPIOParameters=GPIO_Label
PF4.GPIO_Label=AD9833_SDATA_1
PF4.Locked=true
PF4.Signal=GPIO_Output
PF5.GPIOParameters=GPIO_Label
PF5.GPIO_Label=AD9833_FSYNC_2
PF5.Locked=true
PF5.Signal=GPIO_Output
PF6.GPIOParameters=GPIO_Label
PF6.GPIO_Label=AD9833_SCLK_2
PF6.Locked=true
PF6.Signal=GPIO_Output
PF7.GPIOParameters=GPIO_Label
PF7.GPIO_Label=AD9833_SDATA_2
PF7.Locked=true
PF7.Signal=GPIO_Output
PG12.Mode=NorPsramChipSelect4_1
PG12.Signal=FSMC_NE4
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.27.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=CubeMXProject_2023H.ioc
ProjectManager.ProjectName=CubeMXProject_2023H
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.27
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_FSMC_Init-FSMC-false-HAL-true,5-MX_USART1_UART_Init-USART1-false-HAL-true,6-MX_ADC1_Init-ADC1-false-HAL-true,7-MX_TIM8_Init-TIM8-false-HAL-true,8-MX_TIM3_Init-TIM3-false-HAL-true,9-MX_TIM2_Init-TIM2-false-HAL-true,10-MX_ADC3_Init-ADC3-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=336
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=96000000
SH.ADCx_IN6.0=ADC1_IN6,IN6
SH.ADCx_IN6.ConfNb=1
SH.FSMC_A6.0=FSMC_A6,A6_1
SH.FSMC_A6.ConfNb=1
SH.FSMC_D0_DA0.0=FSMC_D0,16b-d1
SH.FSMC_D0_DA0.ConfNb=1
SH.FSMC_D10_DA10.0=FSMC_D10,16b-d1
SH.FSMC_D10_DA10.ConfNb=1
SH.FSMC_D11_DA11.0=FSMC_D11,16b-d1
SH.FSMC_D11_DA11.ConfNb=1
SH.FSMC_D12_DA12.0=FSMC_D12,16b-d1
SH.FSMC_D12_DA12.ConfNb=1
SH.FSMC_D13_DA13.0=FSMC_D13,16b-d1
SH.FSMC_D13_DA13.ConfNb=1
SH.FSMC_D14_DA14.0=FSMC_D14,16b-d1
SH.FSMC_D14_DA14.ConfNb=1
SH.FSMC_D15_DA15.0=FSMC_D15,16b-d1
SH.FSMC_D15_DA15.ConfNb=1
SH.FSMC_D1_DA1.0=FSMC_D1,16b-d1
SH.FSMC_D1_DA1.ConfNb=1
SH.FSMC_D2_DA2.0=FSMC_D2,16b-d1
SH.FSMC_D2_DA2.ConfNb=1
SH.FSMC_D3_DA3.0=FSMC_D3,16b-d1
SH.FSMC_D3_DA3.ConfNb=1
SH.FSMC_D4_DA4.0=FSMC_D4,16b-d1
SH.FSMC_D4_DA4.ConfNb=1
SH.FSMC_D5_DA5.0=FSMC_D5,16b-d1
SH.FSMC_D5_DA5.ConfNb=1
SH.FSMC_D6_DA6.0=FSMC_D6,16b-d1
SH.FSMC_D6_DA6.ConfNb=1
SH.FSMC_D7_DA7.0=FSMC_D7,16b-d1
SH.FSMC_D7_DA7.ConfNb=1
SH.FSMC_D8_DA8.0=FSMC_D8,16b-d1
SH.FSMC_D8_DA8.ConfNb=1
SH.FSMC_D9_DA9.0=FSMC_D9,16b-d1
SH.FSMC_D9_DA9.ConfNb=1
SH.FSMC_NOE.0=FSMC_NOE,Lcd1
SH.FSMC_NOE.ConfNb=1
SH.FSMC_NWE.0=FSMC_NWE,Lcd1
SH.FSMC_NWE.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,TriggerSource_TI1FP1
SH.S_TIM3_CH1.1=TIM3_CH1,Input_Capture1_from_TI1
SH.S_TIM3_CH1.ConfNb=2
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM2.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM2.Period=1000-1
TIM2.Prescaler=84-1
TIM2.TIM_MasterOutputTrigger=TIM_TRGO_RESET
TIM3.Channel-Input_Capture1_from_TI1=TIM_CHANNEL_1
TIM3.IPParameters=Channel-Input_Capture1_from_TI1,Prescaler
TIM3.Prescaler=8400-1
TIM8.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM8.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM8.Period=164-1
TIM8.Prescaler=0
TIM8.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM3_VS_ControllerModeReset.Mode=Reset Mode
VP_TIM3_VS_ControllerModeReset.Signal=TIM3_VS_ControllerModeReset
VP_TIM8_VS_ClockSourceINT.Mode=Internal
VP_TIM8_VS_ClockSourceINT.Signal=TIM8_VS_ClockSourceINT
board=custom
